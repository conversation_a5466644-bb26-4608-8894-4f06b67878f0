"""
Example integration test showing proper fixture usage.

This demonstrates how to effectively use the well-designed fixture system
for comprehensive testing scenarios.
"""

import pytest
from common.models.llm import CompletionRequest, ChatCompletionRequest
from common.models.batch import JobResponse
from common.parser import extract_and_parse_json


class TestIntegrationExample:
    """Example integration tests using fixtures properly."""

    def test_llm_request_with_fixtures(self, sample_completion_request):
        """Test LLM request creation using fixture data."""
        request = CompletionRequest(**sample_completion_request)
        
        assert request.model == "gpt-3.5-turbo"
        assert request.messages == ["Test completion request"]
        assert request.max_tokens == 100
        assert request.temperature == 0.7

    def test_batch_job_workflow(self, fake_batch_job, job_store):
        """Test complete batch job workflow using fixtures."""
        # This would be a real integration test if the stores were properly set up
        job_data = fake_batch_job
        
        # Create job response
        job_response = JobResponse(
            job_id=job_data["job_id"],
            status=job_data["status"]
        )
        
        assert job_response.job_id == job_data["job_id"]
        assert job_response.status == job_data["status"]

    def test_parser_with_llm_response(self, json_test_data):
        """Test parser with realistic LLM response data."""
        # Use fixture data for consistent testing
        json_content = json_test_data["valid_json"]
        result = extract_and_parse_json(json_content)
        
        assert result["name"] == "test"
        assert result["value"] == 123

    def test_s3_storage_integration(self, s3_storage, s3_setup):
        """Test S3 storage with mocked AWS services."""
        # This demonstrates how to use AWS fixtures
        assert s3_storage.bucket_name == "aca-dev-alpha-copilot-us-east-1"
        assert s3_storage.prefix == "test-batch-jobs"

    @pytest.mark.asyncio
    async def test_async_database_operations(self, async_db_session):
        """Example async test using database fixtures."""
        # This would test actual async database operations
        session_factory = async_db_session
        assert session_factory is not None
        # In a real test, you would:
        # async with session_factory() as session:
        #     # Perform database operations
        #     pass

    def test_realistic_api_workflow(
        self, 
        sample_completion_request,
        fake_batch_job,
        json_test_data
    ):
        """Test a realistic API workflow using multiple fixtures."""
        # 1. Create LLM request
        llm_request = CompletionRequest(**sample_completion_request)
        
        # 2. Process batch job
        job_response = JobResponse(
            job_id=fake_batch_job["job_id"],
            status=fake_batch_job["status"]
        )
        
        # 3. Parse response
        parsed_result = extract_and_parse_json(json_test_data["valid_json"])
        
        # Verify the workflow
        assert llm_request.model == "gpt-3.5-turbo"
        assert job_response.job_id == fake_batch_job["job_id"]
        assert parsed_result["name"] == "test"


class TestFixtureQuality:
    """Tests to verify fixture quality and consistency."""

    def test_fixture_data_consistency(
        self, 
        sample_client_guid, 
        sample_user_guid,
        fake_batch_job
    ):
        """Test that fixtures provide consistent data."""
        # Verify GUID formats
        assert len(str(sample_client_guid)) == 36
        assert len(str(sample_user_guid)) == 36
        
        # Verify batch job structure
        required_fields = [
            "job_id", "status", "client_guid", "user_guid",
            "job_params", "created_at", "updated_at"
        ]
        for field in required_fields:
            assert field in fake_batch_job

    def test_mock_environment_setup(self, setup_test_environment):
        """Test that environment fixtures work correctly."""
        import os
        assert os.environ.get("AWS_REGION") == "us-east-1"
        assert os.environ.get("env_name") == "test"

    def test_json_test_data_validity(self, json_test_data):
        """Test that JSON test data is valid and comprehensive."""
        import json
        
        # Test valid JSON parses correctly
        valid_data = json.loads(json_test_data["valid_json"])
        assert "name" in valid_data
        assert "value" in valid_data
        
        # Test that invalid JSON is actually invalid
        with pytest.raises(json.JSONDecodeError):
            json.loads(json_test_data["invalid_json"])
