"""Concise tests for common.models.batch module."""

import json
import uuid

import pytest
from pydantic import ValidationError

from common.models.batch import JobResponse, JobStatusResponse, JobResultResponse


class TestJobResponse:
    """Test cases for JobResponse model."""

    def test_creation(self):
        """Test creating JobResponse with valid UUID."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, uuid.UUID)

    def test_from_string_uuid(self):
        """Test creating JobResponse from string UUID."""
        job_id_str = "12345678-1234-5678-9abc-123456789012"
        response = JobResponse(job_id=job_id_str)

        assert response.job_id == uuid.UUID(job_id_str)
        assert isinstance(response.job_id, uuid.UUID)

    def test_invalid_uuid(self):
        """Test JobResponse creation with invalid UUID."""
        with pytest.raises(ValidationError) as exc_info:
            JobResponse(job_id="invalid-uuid")

        assert "job_id" in str(exc_info.value)

    def test_missing_job_id(self):
        """Test JobResponse creation without job_id."""
        with pytest.raises(ValidationError) as exc_info:
            JobResponse()

        assert "job_id" in str(exc_info.value)

    def test_json_serialization(self):
        """Test JobResponse JSON serialization with UUID encoder."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test model_dump
        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

        # Test JSON string serialization
        json_str = response.model_dump_json()
        assert str(job_id) in json_str

        # Verify it can be parsed back
        parsed = json.loads(json_str)
        assert parsed["job_id"] == str(job_id)

    def test_equality(self):
        """Test JobResponse equality comparison."""
        job_id = uuid.uuid4()
        response1 = JobResponse(job_id=job_id)
        response2 = JobResponse(job_id=job_id)
        response3 = JobResponse(job_id=uuid.uuid4())

        assert response1 == response2
        assert response1 != response3


class TestJobStatusResponse:
    """Test cases for JobStatusResponse model."""

    def test_minimal_creation(self):
        """Test creating JobStatusResponse with minimal required fields."""
        response = JobStatusResponse(
            job_id="12345678-1234-5678-9abc-123456789012",
            status="PENDING"
        )

        assert response.job_id == "12345678-1234-5678-9abc-123456789012"
        assert response.status == "PENDING"
        assert response.status_description is None
        assert response.progress == {}
        assert response.created_at is None

    def test_all_fields(self):
        """Test JobStatusResponse with all fields populated."""
        progress_data = {"processed": 50, "total": 100, "percentage": 50.0}
        
        response = JobStatusResponse(
            job_id="test-job-id",
            status="IN_PROGRESS",
            status_description="Processing batch job",
            progress=progress_data,
            created_at="2023-10-01T10:00:00Z",
            updated_at="2023-10-01T10:30:00Z",
            started_at="2023-10-01T10:05:00Z",
            completed_at=None,
            s3_prefix="batch-jobs/test-job-id"
        )

        assert response.job_id == "test-job-id"
        assert response.status == "IN_PROGRESS"
        assert response.status_description == "Processing batch job"
        assert response.progress == progress_data
        assert response.s3_prefix == "batch-jobs/test-job-id"

    def test_missing_required_fields(self):
        """Test JobStatusResponse validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            JobStatusResponse(status="PENDING")  # Missing job_id

        assert "job_id" in str(exc_info.value)

    def test_json_serialization(self):
        """Test JobStatusResponse JSON serialization."""
        response = JobStatusResponse(
            job_id="serialization-test",
            status="COMPLETED",
            progress={"processed": 100, "total": 100}
        )

        json_data = response.model_dump()
        assert json_data["job_id"] == "serialization-test"
        assert json_data["status"] == "COMPLETED"
        assert json_data["progress"]["processed"] == 100

        json_str = response.model_dump_json()
        assert "serialization-test" in json_str


class TestJobResultResponse:
    """Test cases for JobResultResponse model."""

    def test_minimal_creation(self):
        """Test creating JobResultResponse with minimal required fields."""
        response = JobResultResponse(
            job_id="result-job-id",
            status="COMPLETED"
        )

        assert response.job_id == "result-job-id"
        assert response.status == "COMPLETED"
        assert response.status_description is None
        assert response.result_urls == []
        assert response.pagination == {}

    def test_all_fields(self):
        """Test JobResultResponse with all fields populated."""
        result_urls = [
            "s3://bucket/results/file1.json",
            "s3://bucket/results/file2.json"
        ]
        pagination = {"page": 1, "per_page": 10, "total": 2}

        response = JobResultResponse(
            job_id="complete-job-id",
            status="COMPLETED",
            status_description="Job completed successfully",
            result_urls=result_urls,
            pagination=pagination
        )

        assert response.job_id == "complete-job-id"
        assert response.status == "COMPLETED"
        assert response.status_description == "Job completed successfully"
        assert response.result_urls == result_urls
        assert response.pagination == pagination

    def test_missing_required_fields(self):
        """Test JobResultResponse validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            JobResultResponse(job_id="test")  # Missing status

        assert "status" in str(exc_info.value)

    def test_json_serialization(self):
        """Test JobResultResponse JSON serialization."""
        response = JobResultResponse(
            job_id="json-test-job",
            status="COMPLETED",
            result_urls=["s3://bucket/result.json"]
        )

        json_data = response.model_dump()
        assert json_data["job_id"] == "json-test-job"
        assert json_data["status"] == "COMPLETED"
        assert json_data["result_urls"] == ["s3://bucket/result.json"]


class TestBatchModelsIntegration:
    """Integration tests for all batch models."""

    def test_uuid_encoding_consistency(self):
        """Test that all models handle UUID encoding consistently."""
        job_id = uuid.uuid4()

        # JobResponse uses UUID directly
        job_response = JobResponse(job_id=job_id)
        job_response_json = job_response.model_dump()
        assert job_response_json["job_id"] == job_id

        # Other models use string job_id
        status_response = JobStatusResponse(job_id=str(job_id), status="PENDING")
        result_response = JobResultResponse(job_id=str(job_id), status="COMPLETED")

        status_json = status_response.model_dump()
        result_json = result_response.model_dump()

        assert status_json["job_id"] == str(job_id)
        assert result_json["job_id"] == str(job_id)

    def test_serialization_roundtrip(self):
        """Test serialization roundtrip for all models."""
        job_id = uuid.uuid4()

        # Test JobResponse roundtrip
        original_job = JobResponse(job_id=job_id)
        job_json = original_job.model_dump_json()
        job_data = json.loads(job_json)
        recreated_job = JobResponse(job_id=job_data["job_id"])
        assert recreated_job.job_id == original_job.job_id

        # Test JobStatusResponse roundtrip
        original_status = JobStatusResponse(job_id=str(job_id), status="PENDING")
        status_json = original_status.model_dump_json()
        status_data = json.loads(status_json)
        recreated_status = JobStatusResponse(**status_data)
        assert recreated_status.job_id == original_status.job_id
        assert recreated_status.status == original_status.status

    def test_realistic_workflow(self):
        """Test realistic batch job workflow with all models."""
        job_id = uuid.uuid4()

        # 1. Job submission
        job_response = JobResponse(job_id=job_id)
        assert job_response.job_id == job_id

        # 2. Job status check
        status_response = JobStatusResponse(
            job_id=str(job_id),
            status="IN_PROGRESS",
            progress={"processed": 50, "total": 100}
        )
        assert status_response.job_id == str(job_id)
        assert status_response.status == "IN_PROGRESS"

        # 3. Job completion
        result_response = JobResultResponse(
            job_id=str(job_id),
            status="COMPLETED",
            result_urls=["s3://bucket/results/output.json"]
        )
        assert result_response.job_id == str(job_id)
        assert result_response.status == "COMPLETED"
        assert len(result_response.result_urls) == 1
