"""Tests for common.models.batch module."""

import json
import uuid

import pytest
from pydantic import ValidationError

from common.models.batch import JobResponse, JobStatusResponse, JobResultResponse


class TestJobResponse:
    """Test cases for JobResponse model."""

    def test_job_response_valid_creation(self):
        """Test creating JobResponse with valid data."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, uuid.UUID)

    def test_job_response_from_string_uuid(self):
        """Test creating JobResponse from string UUID."""
        job_id_str = "12345678-1234-5678-9abc-123456789012"
        response = JobResponse(job_id=job_id_str)

        assert response.job_id == uuid.UUID(job_id_str)
        assert isinstance(response.job_id, uuid.UUID)

    def test_job_response_invalid_uuid(self):
        """Test JobResponse creation with invalid UUID."""
        with pytest.raises(ValidationError) as exc_info:
            JobResponse(job_id="invalid-uuid")

        assert "job_id" in str(exc_info.value)

    def test_job_response_missing_job_id(self):
        """Test JobResponse creation without job_id."""
        with pytest.raises(ValidationError) as exc_info:
            JobResponse()  # type: ignore

        assert "job_id" in str(exc_info.value)

    def test_job_response_json_serialization(self):
        """Test JobResponse JSON serialization with UUID encoder."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test model_dump
        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

    def test_job_response_json_serialization_to_string(self):
        """Test JobResponse serialization to JSON string."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Test JSON serialization
        json_str = response.model_dump_json()
        assert str(job_id) in json_str

        # Verify it can be parsed back
        parsed = json.loads(json_str)
        assert parsed["job_id"] == str(job_id)

    def test_job_response_config_class(self):
        """Test JobResponse Config class properties."""
        response = JobResponse(job_id=uuid.uuid4())

        # Check that Config exists and has the expected encoder
        assert hasattr(response, "model_config") or hasattr(JobResponse, "Config")

    def test_job_response_field_info(self):
        """Test JobResponse field information and metadata."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Verify the field exists and has correct type
        assert hasattr(response, "job_id")
        assert isinstance(response.job_id, uuid.UUID)

    def test_job_response_equality(self):
        """Test JobResponse equality comparison."""
        job_id = uuid.uuid4()
        response1 = JobResponse(job_id=job_id)
        response2 = JobResponse(job_id=job_id)
        response3 = JobResponse(job_id=uuid.uuid4())

        assert response1 == response2
        assert response1 != response3

    def test_job_response_hash(self):
        """Test JobResponse hashing."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        # Should be hashable
        hash_value = hash(response)
        assert isinstance(hash_value, int)

    def test_job_response_repr(self):
        """Test JobResponse string representation."""
        job_id = uuid.uuid4()
        response = JobResponse(job_id=job_id)

        repr_str = repr(response)
        assert "JobResponse" in repr_str
        assert str(job_id) in repr_str


class TestJobStatusResponse:
    """Test cases for JobStatusResponse model."""

    def test_job_status_response_minimal_valid(self):
        """Test JobStatusResponse with minimal required fields."""
        response = JobStatusResponse(
            job_id="test-job-123",
            status="PENDING"
        )

        assert response.job_id == "test-job-123"
        assert response.status == "PENDING"
        assert response.status_description is None
        assert response.progress == {}
        assert response.created_at is None
        assert response.updated_at is None
        assert response.started_at is None
        assert response.completed_at is None
        assert response.s3_prefix is None

    def test_job_status_response_all_fields(self):
        """Test JobStatusResponse with all fields populated."""
        progress_data = {"processed": 50, "total": 100}
        response = JobStatusResponse(
            job_id="test-job-123",
            status="PROCESSING",
            status_description="Processing batch items",
            progress=progress_data,
            created_at="2023-01-01T00:00:00Z",
            updated_at="2023-01-01T00:30:00Z",
            started_at="2023-01-01T00:01:00Z",
            completed_at=None,
            s3_prefix="s3://bucket/prefix/"
        )

        assert response.job_id == "test-job-123"
        assert response.status == "PROCESSING"
        assert response.status_description == "Processing batch items"
        assert response.progress == progress_data
        assert response.created_at == "2023-01-01T00:00:00Z"
        assert response.updated_at == "2023-01-01T00:30:00Z"
        assert response.started_at == "2023-01-01T00:01:00Z"
        assert response.completed_at is None
        assert response.s3_prefix == "s3://bucket/prefix/"

    def test_job_status_response_missing_required_fields(self):
        """Test JobStatusResponse creation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            JobStatusResponse(job_id="test-job")  # Missing status

        assert "status" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            JobStatusResponse(status="PENDING")  # Missing job_id

        assert "job_id" in str(exc_info.value)

    def test_job_status_response_default_values(self):
        """Test JobStatusResponse default values."""
        response = JobStatusResponse(
            job_id="test-job-123",
            status="PENDING"
        )

        # Test default factory for progress
        assert response.progress == {}
        assert isinstance(response.progress, dict)

        # Modifying one instance shouldn't affect another
        response.progress["test"] = "value"
        response2 = JobStatusResponse(job_id="test-job-456", status="PENDING")
        assert response2.progress == {}

    def test_job_status_response_complex_progress(self):
        """Test JobStatusResponse with complex progress data."""
        complex_progress = {
            "processed": 75,
            "total": 150,
            "errors": 5,
            "batches": {
                "completed": 3,
                "failed": 1,
                "pending": 2
            },
            "timestamps": ["2023-01-01T00:00:00Z", "2023-01-01T00:30:00Z"]
        }

        response = JobStatusResponse(
            job_id="test-job-123",
            status="PROCESSING",
            progress=complex_progress
        )

        assert response.progress == complex_progress
        assert response.progress["batches"]["completed"] == 3  # type: ignore

    def test_job_status_response_json_serialization(self):
        """Test JobStatusResponse JSON serialization."""
        response = JobStatusResponse(
            job_id="test-job-123",
            status="COMPLETED",
            progress={"processed": 100, "total": 100}
        )

        json_data = response.model_dump()
        assert json_data["job_id"] == "test-job-123"
        assert json_data["status"] == "COMPLETED"
        assert json_data["progress"] == {"processed": 100, "total": 100}

    def test_job_status_response_json_string_serialization(self):
        """Test JobStatusResponse JSON string serialization."""
        response = JobStatusResponse(
            job_id="test-job-123",
            status="COMPLETED"
        )

        json_str = response.model_dump_json()
        parsed = json.loads(json_str)

        assert parsed["job_id"] == "test-job-123"
        assert parsed["status"] == "COMPLETED"
        assert parsed["progress"] == {}

    def test_job_status_response_config_class(self):
        """Test JobStatusResponse Config class properties."""
        response = JobStatusResponse(job_id="test", status="test")

        # Check that Config exists
        assert hasattr(response, "model_config") or hasattr(JobStatusResponse, "Config")

    def test_job_status_response_field_validation(self):
        """Test JobStatusResponse field validation."""
        # Test that string fields accept strings
        response = JobStatusResponse(
            job_id="string-id",
            status="string-status",
            status_description="string description"
        )

        assert isinstance(response.job_id, str)
        assert isinstance(response.status, str)
        assert isinstance(response.status_description, str)

    def test_job_status_response_none_values(self):
        """Test JobStatusResponse with None values for optional fields."""
        response = JobStatusResponse(
            job_id="test-job",
            status="PENDING",
            status_description=None,
            created_at=None,
            updated_at=None,
            started_at=None,
            completed_at=None,
            s3_prefix=None
        )

        assert response.status_description is None
        assert response.created_at is None
        assert response.updated_at is None
        assert response.started_at is None
        assert response.completed_at is None
        assert response.s3_prefix is None

    def test_job_status_response_empty_strings(self):
        """Test JobStatusResponse with empty strings."""
        response = JobStatusResponse(
            job_id="",  # Empty string should be valid
            status="",
            status_description="",
            s3_prefix=""
        )

        assert response.job_id == ""
        assert response.status == ""
        assert response.status_description == ""
        assert response.s3_prefix == ""


class TestJobResultResponse:
    """Test cases for JobResultResponse model."""

    def test_job_result_response_minimal_valid(self):
        """Test JobResultResponse with minimal required fields."""
        response = JobResultResponse(
            job_id="test-job-123",
            status="COMPLETED"
        )

        assert response.job_id == "test-job-123"
        assert response.status == "COMPLETED"
        assert response.status_description is None
        assert response.result_urls == []
        assert response.pagination == {}

    def test_job_result_response_all_fields(self):
        """Test JobResultResponse with all fields populated."""
        result_urls = [
            "https://s3.amazonaws.com/bucket/file1.json",
            "https://s3.amazonaws.com/bucket/file2.json"
        ]
        pagination_data = {"page": 1, "per_page": 10, "total": 25}

        response = JobResultResponse(
            job_id="test-job-123",
            status="COMPLETED",
            status_description="Job completed successfully",
            result_urls=result_urls,
            pagination=pagination_data
        )

        assert response.job_id == "test-job-123"
        assert response.status == "COMPLETED"
        assert response.status_description == "Job completed successfully"
        assert response.result_urls == result_urls
        assert response.pagination == pagination_data

    def test_job_result_response_missing_required_fields(self):
        """Test JobResultResponse creation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            JobResultResponse(job_id="test-job")  # Missing status

        assert "status" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            JobResultResponse(status="COMPLETED")  # Missing job_id

        assert "job_id" in str(exc_info.value)

    def test_job_result_response_default_values(self):
        """Test JobResultResponse default values."""
        response = JobResultResponse(
            job_id="test-job-123",
            status="COMPLETED"
        )

        # Test default factories
        assert response.result_urls == []
        assert response.pagination == {}
        assert isinstance(response.result_urls, list)
        assert isinstance(response.pagination, dict)

        # Modifying one instance shouldn't affect another
        response.result_urls.append("test-url")
        response.pagination["test"] = "value"

        response2 = JobResultResponse(job_id="test-job-456", status="COMPLETED")
        assert response2.result_urls == []
        assert response2.pagination == {}

    def test_job_result_response_complex_data(self):
        """Test JobResultResponse with complex data structures."""
        complex_urls = [
            "s3://bucket1/path/to/file1.json",
            "s3://bucket2/path/to/file2.csv",
            "https://external.api.com/result/123"
        ]

        complex_pagination = {
            "current_page": 2,
            "per_page": 50,
            "total_items": 1000,
            "total_pages": 20,
            "has_next": True,
            "has_prev": True,
            "next_page": 3,
            "prev_page": 1
        }

        response = JobResultResponse(
            job_id="complex-job-456",
            status="COMPLETED",
            result_urls=complex_urls,
            pagination=complex_pagination
        )

        assert response.result_urls == complex_urls
        assert response.pagination == complex_pagination
        assert len(response.result_urls) == 3
        assert response.pagination["total_pages"] == 20  # type: ignore

    def test_job_result_response_json_serialization(self):
        """Test JobResultResponse JSON serialization."""
        response = JobResultResponse(
            job_id="test-job-123",
            status="COMPLETED",
            result_urls=["url1", "url2"],
            pagination={"page": 1, "total": 10}
        )

        json_data = response.model_dump()
        assert json_data["job_id"] == "test-job-123"
        assert json_data["status"] == "COMPLETED"
        assert json_data["result_urls"] == ["url1", "url2"]
        assert json_data["pagination"] == {"page": 1, "total": 10}

    def test_job_result_response_json_string_serialization(self):
        """Test JobResultResponse JSON string serialization."""
        response = JobResultResponse(
            job_id="test-job-123",
            status="FAILED",
            status_description="Processing failed"
        )

        json_str = response.model_dump_json()
        parsed = json.loads(json_str)

        assert parsed["job_id"] == "test-job-123"
        assert parsed["status"] == "FAILED"
        assert parsed["status_description"] == "Processing failed"
        assert parsed["result_urls"] == []
        assert parsed["pagination"] == {}

    def test_job_result_response_config_class(self):
        """Test JobResultResponse Config class properties."""
        response = JobResultResponse(job_id="test", status="test")

        # Check that Config exists
        assert hasattr(response, "model_config") or hasattr(JobResultResponse, "Config")

    def test_job_result_response_empty_collections(self):
        """Test JobResultResponse with explicitly empty collections."""
        response = JobResultResponse(
            job_id="test-job",
            status="COMPLETED",
            result_urls=[],
            pagination={}
        )

        assert response.result_urls == []
        assert response.pagination == {}
        assert len(response.result_urls) == 0
        assert len(response.pagination) == 0

    def test_job_result_response_list_mutation(self):
        """Test that result_urls list can be mutated after creation."""
        response = JobResultResponse(
            job_id="test-job",
            status="COMPLETED"
        )

        # Should be able to modify the list
        response.result_urls.append("new-url")
        assert "new-url" in response.result_urls
        assert len(response.result_urls) == 1

    def test_job_result_response_dict_mutation(self):
        """Test that pagination dict can be mutated after creation."""
        response = JobResultResponse(
            job_id="test-job",
            status="COMPLETED"
        )

        # Should be able to modify the dict
        response.pagination["new_key"] = "new_value"
        assert response.pagination["new_key"] == "new_value"
        assert len(response.pagination) == 1


class TestBatchModelsIntegration:
    """Integration tests for all batch models."""

    def test_all_models_uuid_encoding(self):
        """Test that all models handle UUID encoding consistently."""
        job_id = uuid.uuid4()

        # JobResponse uses UUID directly
        job_response = JobResponse(job_id=job_id)
        job_response_json = job_response.model_dump()
        assert job_response_json["job_id"] == job_id

        # Other models use string job_id
        status_response = JobStatusResponse(job_id=str(job_id), status="PENDING")
        result_response = JobResultResponse(job_id=str(job_id), status="COMPLETED")

        status_json = status_response.model_dump()
        result_json = result_response.model_dump()

        assert status_json["job_id"] == str(job_id)
        assert result_json["job_id"] == str(job_id)

    def test_all_models_serialization_roundtrip(self):
        """Test serialization roundtrip for all models."""
        job_id = uuid.uuid4()

        # JobResponse
        job_response = JobResponse(job_id=job_id)
        job_json_str = job_response.model_dump_json()
        job_parsed = json.loads(job_json_str)
        job_recreated = JobResponse(**job_parsed)
        assert str(job_recreated.job_id) == str(job_id)

        # JobStatusResponse
        status_response = JobStatusResponse(
            job_id=str(job_id),
            status="PROCESSING",
            progress={"done": 50}
        )
        status_json_str = status_response.model_dump_json()
        status_parsed = json.loads(status_json_str)
        status_recreated = JobStatusResponse(**status_parsed)
        assert status_recreated.job_id == str(job_id)
        assert status_recreated.progress == {"done": 50}

        # JobResultResponse
        result_response = JobResultResponse(
            job_id=str(job_id),
            status="COMPLETED",
            result_urls=["url1", "url2"]
        )
        result_json_str = result_response.model_dump_json()
        result_parsed = json.loads(result_json_str)
        result_recreated = JobResultResponse(**result_parsed)
        assert result_recreated.job_id == str(job_id)
        assert result_recreated.result_urls == ["url1", "url2"]

    def test_models_with_real_world_data(self):
        """Test models with realistic data scenarios."""
        job_id = uuid.uuid4()

        # Realistic job creation
        job_response = JobResponse(job_id=job_id)

        # Realistic status updates
        status_updates = [
            JobStatusResponse(
                job_id=str(job_id),
                status="PENDING",
                created_at="2023-10-01T10:00:00Z"
            ),
            JobStatusResponse(
                job_id=str(job_id),
                status="PROCESSING",
                status_description="Processing 500 items",
                progress={"processed": 250, "total": 500, "percentage": 50.0},
                started_at="2023-10-01T10:01:00Z",
                updated_at="2023-10-01T10:15:00Z"
            ),
            JobStatusResponse(
                job_id=str(job_id),
                status="COMPLETED",
                status_description="All items processed successfully",
                progress={"processed": 500, "total": 500, "percentage": 100.0},
                completed_at="2023-10-01T10:30:00Z",
                s3_prefix="s3://my-bucket/results/batch-" + str(job_id)
            )
        ]

        # Realistic result response
        result_response = JobResultResponse(
            job_id=str(job_id),
            status="COMPLETED",
            result_urls=[
                f"s3://my-bucket/results/batch-{job_id}/part-001.json",
                f"s3://my-bucket/results/batch-{job_id}/part-002.json",
                f"s3://my-bucket/results/batch-{job_id}/summary.json"
            ],
            pagination={
                "total_files": 3,
                "total_size_bytes": 1048576,
                "compression": "gzip"
            }
        )

        # Verify all models work with realistic data
        assert job_response.job_id == job_id
        assert len(status_updates) == 3
        assert status_updates[2].status == "COMPLETED"
        assert len(result_response.result_urls) == 3
        assert result_response.pagination["total_files"] == 3  # type: ignore
