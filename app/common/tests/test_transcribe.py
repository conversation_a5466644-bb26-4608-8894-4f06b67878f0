"""Tests for common.models.transcribe module."""

import io
from unittest.mock import Mock

import pytest
from fastapi import UploadFile
from pydantic import ValidationError

from common.models.transcribe import TranscriptionRequest, TranscriptionJobResponse


class TestTranscriptionRequest:
    """Test cases for TranscriptionRequest model."""

    def test_transcription_request_with_upload_file(self):
        """Test TranscriptionRequest with UploadFile."""
        # Create a mock UploadFile
        file_content = b"mock audio content"
        upload_file = UploadFile(
            filename="test_audio.mp3",
            file=io.BytesIO(file_content)
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code="en-US",
            media_format="mp3",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.media_format == "mp3"
        assert request.user == "test_user"

    def test_transcription_request_with_string_url(self):
        """Test TranscriptionRequest with presigned S3 URL string."""
        s3_url = "https://s3.amazonaws.com/bucket/audio.wav?presigned=true"

        request = TranscriptionRequest(
            file=s3_url,
            language_code="es-ES",
            media_format="wav",
            user="user123"
        )

        assert request.file == s3_url
        assert request.language_code == "es-ES"
        assert request.media_format == "wav"
        assert request.user == "user123"

    def test_transcription_request_minimal_fields(self):
        """Test TranscriptionRequest with only required field."""
        upload_file = UploadFile(
            filename="minimal.mp3",
            file=io.BytesIO(b"content")
        )

        request = TranscriptionRequest(file=upload_file)

        assert request.file == upload_file
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_transcription_request_string_file_minimal(self):
        """Test TranscriptionRequest with string file and minimal fields."""
        s3_url = "s3://bucket/audio/file.mp3"

        request = TranscriptionRequest(file=s3_url)

        assert request.file == s3_url
        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_transcription_request_missing_file(self):
        """Test TranscriptionRequest creation without file."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionRequest(
                language_code="en-US",
                user="test"
            )

        assert "file" in str(exc_info.value)

    def test_transcription_request_arbitrary_types_allowed(self):
        """Test that TranscriptionRequest allows arbitrary types (UploadFile)."""
        upload_file = UploadFile(
            file=io.BytesIO(b"video content"),
            filename="test.mp4"
        )

        # Should not raise validation error for UploadFile type
        request = TranscriptionRequest(file=upload_file)
        assert request.file == upload_file

    def test_transcription_request_json_serialization_with_string(self):
        """Test TranscriptionRequest JSON serialization with string file."""
        s3_url = "s3://transcription-bucket/audio.wav"

        request = TranscriptionRequest(
            file=s3_url,
            language_code="fr-FR",
            media_format="wav",
            user="french_user"
        )

        json_data = request.model_dump()
        assert json_data["file"] == s3_url
        assert json_data["language_code"] == "fr-FR"
        assert json_data["media_format"] == "wav"
        assert json_data["user"] == "french_user"

    def test_transcription_request_json_serialization_with_upload_file(self):
        """Test TranscriptionRequest JSON serialization with UploadFile."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"content")
        )

        request = TranscriptionRequest(file=upload_file)

        # Should be able to dump to dict (may contain UploadFile object)
        json_data = request.model_dump()
        assert json_data["file"] == upload_file

    def test_transcription_request_empty_optional_fields(self):
        """Test TranscriptionRequest with empty string optional fields."""
        s3_url = "s3://bucket/file.mp3"

        request = TranscriptionRequest(
            file=s3_url,
            language_code="",
            media_format="",
            user=""
        )

        assert request.language_code == ""
        assert request.media_format == ""
        assert request.user == ""

    def test_transcription_request_none_optional_fields(self):
        """Test TranscriptionRequest with None optional fields."""
        upload_file = UploadFile(
            filename="test.wav",
            file=io.BytesIO(b"audio")
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code=None,
            media_format=None,
            user=None
        )

        assert request.language_code is None
        assert request.media_format is None
        assert request.user is None

    def test_transcription_request_various_file_formats(self):
        """Test TranscriptionRequest with various media formats."""
        formats = ["mp3", "wav", "m4a", "flac", "ogg", "mp4", "avi", "mov"]

        for fmt in formats:
            s3_url = f"s3://bucket/audio.{fmt}"
            request = TranscriptionRequest(
                file=s3_url,
                media_format=fmt
            )
            assert request.media_format == fmt

    def test_transcription_request_various_language_codes(self):
        """Test TranscriptionRequest with various language codes."""
        language_codes = [
            "en-US", "en-GB", "es-ES", "es-MX", "fr-FR", "de-DE",
            "ja-JP", "ko-KR", "zh-CN", "pt-BR", "it-IT", "ru-RU"
        ]

        for lang in language_codes:
            request = TranscriptionRequest(
                file="s3://bucket/audio.mp3",
                language_code=lang
            )
            assert request.language_code == lang

    def test_transcription_request_long_user_identifier(self):
        """Test TranscriptionRequest with long user identifier."""
        long_user = "user_" + "x" * 1000  # Very long user ID

        request = TranscriptionRequest(
            file="s3://bucket/audio.mp3",
            user=long_user
        )

        assert request.user == long_user

    def test_transcription_request_special_characters_in_fields(self):
        """Test TranscriptionRequest with special characters."""
        request = TranscriptionRequest(
            file="s3://bucket/audio file with spaces & symbols!.mp3",
            language_code="en-US",
            media_format="mp3",
            user="<EMAIL>"
        )

        assert "spaces & symbols!" in request.file
        assert request.user == "<EMAIL>"

    def test_transcription_request_unicode_in_fields(self):
        """Test TranscriptionRequest with Unicode characters."""
        request = TranscriptionRequest(
            file="s3://bucket/音频文件.mp3",
            language_code="zh-CN",
            user="用户123"
        )

        assert "音频文件" in request.file
        assert request.language_code == "zh-CN"
        assert request.user == "用户123"


class TestTranscriptionRequestAsForm:
    """Test cases for TranscriptionRequest.as_form method."""

    def test_as_form_with_upload_file(self):
        """Test as_form with UploadFile."""
        upload_file = UploadFile(
            file=io.BytesIO(b"audio content"),
            filename="test.mp3"
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US",
            user="test_user"
        )

        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert request.user == "test_user"
        assert request.media_format is None

    def test_as_form_with_file_url(self):
        """Test as_form with file_url."""
        file_url = "https://example.com/audio.wav"

        request = TranscriptionRequest.as_form(
            file_url=file_url,
            language_code="es-ES",
            user="spanish_user"
        )

        assert request.file == file_url
        assert request.language_code == "es-ES"
        assert request.user == "spanish_user"

    def test_as_form_with_both_file_and_url(self):
        """Test as_form with both file and file_url (file takes precedence)."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"content")
        )
        file_url = "https://example.com/audio.wav"

        request = TranscriptionRequest.as_form(
            file=upload_file,
            file_url=file_url,
            language_code="en-US"
        )

        # File should take precedence over file_url
        assert request.file == upload_file
        assert request.language_code == "en-US"

    def test_as_form_missing_both_file_and_url(self):
        """Test as_form with neither file nor file_url."""
        with pytest.raises(ValueError) as exc_info:
            TranscriptionRequest.as_form(
                language_code="en-US",
                user="test"
            )

        assert "file or file_url must be provided" in str(exc_info.value)

    def test_as_form_minimal_with_file(self):
        """Test as_form with minimal parameters using file."""
        upload_file = UploadFile(
            filename="minimal.wav",
            file=io.BytesIO(b"audio")
        )

        request = TranscriptionRequest.as_form(file=upload_file)

        assert request.file == upload_file
        assert request.language_code is None
        assert request.user is None

    def test_as_form_minimal_with_url(self):
        """Test as_form with minimal parameters using file_url."""
        file_url = "s3://bucket/audio.mp3"

        request = TranscriptionRequest.as_form(file_url=file_url)

        assert request.file == file_url
        assert request.language_code is None
        assert request.user is None

    def test_as_form_none_values(self):
        """Test as_form with None values for optional parameters."""
        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"content")
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code=None,
            user=None
        )

        assert request.file == upload_file
        assert request.language_code is None
        assert request.user is None

    def test_as_form_empty_string_values(self):
        """Test as_form with empty string values."""
        file_url = "s3://bucket/audio.mp3"

        request = TranscriptionRequest.as_form(
            file_url=file_url,
            language_code="",
            user=""
        )

        assert request.file == file_url
        assert request.language_code == ""
        assert request.user == ""

    def test_as_form_classmethod_annotation(self):
        """Test that as_form is properly annotated as a classmethod."""
        # Verify as_form is a classmethod and returns TranscriptionRequest
        assert callable(TranscriptionRequest.as_form)

        upload_file = UploadFile(
            filename="test.mp3",
            file=io.BytesIO(b"content")
        )

        result = TranscriptionRequest.as_form(file=upload_file)
        assert isinstance(result, TranscriptionRequest)

    def test_as_form_with_real_upload_file(self):
        """Test as_form with more realistic UploadFile."""
        file_content = b"fake audio data for testing purposes"
        upload_file = UploadFile(
            file=io.BytesIO(file_content),
            filename="recording_2023-10-01.mp3",
            size=len(file_content)
        )

        request = TranscriptionRequest.as_form(
            file=upload_file,
            language_code="en-US",
            user="mobile_app_user_123"
        )

        assert request.file == upload_file
        assert request.file.filename == "recording_2023-10-01.mp3"
        assert request.language_code == "en-US"
        assert request.user == "mobile_app_user_123"


class TestTranscriptionJobResponse:
    """Test cases for TranscriptionJobResponse model."""

    def test_transcription_job_response_creation(self):
        """Test creating TranscriptionJobResponse with valid data."""
        job_id = "transcribe-job-12345678-1234-5678-9abc-123456789012"

        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id
        assert isinstance(response.job_id, str)

    def test_transcription_job_response_uuid_format(self):
        """Test TranscriptionJobResponse with UUID-like job_id."""
        job_id = "12345678-1234-5678-9abc-123456789012"

        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id

    def test_transcription_job_response_custom_format(self):
        """Test TranscriptionJobResponse with custom job_id format."""
        job_id = "batch_transcription_2023_10_01_v2"

        response = TranscriptionJobResponse(job_id=job_id)

        assert response.job_id == job_id

    def test_transcription_job_response_missing_job_id(self):
        """Test TranscriptionJobResponse creation without job_id."""
        with pytest.raises(ValidationError) as exc_info:
            TranscriptionJobResponse()

        assert "job_id" in str(exc_info.value)

    def test_transcription_job_response_empty_job_id(self):
        """Test TranscriptionJobResponse with empty job_id."""
        response = TranscriptionJobResponse(job_id="")

        assert response.job_id == ""

    def test_transcription_job_response_json_serialization(self):
        """Test TranscriptionJobResponse JSON serialization."""
        job_id = "transcribe-job-abcdef123456"

        response = TranscriptionJobResponse(job_id=job_id)

        json_data = response.model_dump()
        assert json_data["job_id"] == job_id

    def test_transcription_job_response_json_string_serialization(self):
        """Test TranscriptionJobResponse JSON string serialization."""
        job_id = "job_987654321"

        response = TranscriptionJobResponse(job_id=job_id)

        json_str = response.model_dump_json()
        assert job_id in json_str

        # Verify it can be parsed back
        import json
        parsed = json.loads(json_str)
        assert parsed["job_id"] == job_id

    def test_transcription_job_response_field_description(self):
        """Test TranscriptionJobResponse field description."""
        response = TranscriptionJobResponse(job_id="test")

        # Verify field exists and has correct type
        assert hasattr(response, "job_id")
        assert isinstance(response.job_id, str)

    def test_transcription_job_response_equality(self):
        """Test TranscriptionJobResponse equality comparison."""
        job_id = "same-job-id"
        response1 = TranscriptionJobResponse(job_id=job_id)
        response2 = TranscriptionJobResponse(job_id=job_id)
        response3 = TranscriptionJobResponse(job_id="different-job-id")

        assert response1 == response2
        assert response1 != response3

    def test_transcription_job_response_hash(self):
        """Test TranscriptionJobResponse hashing."""
        response = TranscriptionJobResponse(job_id="hash-test")

        # Should be hashable
        hash_value = hash(response)
        assert isinstance(hash_value, int)

    def test_transcription_job_response_repr(self):
        """Test TranscriptionJobResponse string representation."""
        job_id = "repr-test-job"
        response = TranscriptionJobResponse(job_id=job_id)

        repr_str = repr(response)
        assert "TranscriptionJobResponse" in repr_str
        assert job_id in repr_str

    def test_transcription_job_response_long_job_id(self):
        """Test TranscriptionJobResponse with very long job_id."""
        long_job_id = "job_" + "x" * 1000

        response = TranscriptionJobResponse(job_id=long_job_id)

        assert response.job_id == long_job_id
        assert len(response.job_id) > 1000

    def test_transcription_job_response_special_characters(self):
        """Test TranscriptionJobResponse with special characters in job_id."""
        special_job_id = "job-with_special.chars@domain:8080/path?param=value"

        response = TranscriptionJobResponse(job_id=special_job_id)

        assert response.job_id == special_job_id

    def test_transcription_job_response_unicode_job_id(self):
        """Test TranscriptionJobResponse with Unicode characters in job_id."""
        unicode_job_id = "作业_任务_123_🎵"

        response = TranscriptionJobResponse(job_id=unicode_job_id)

        assert response.job_id == unicode_job_id


class TestTranscribeModelsIntegration:
    """Integration tests for transcribe models."""

    def test_request_response_integration(self):
        """Test integration between TranscriptionRequest and TranscriptionJobResponse."""
        # Simulate a typical workflow

        # 1. Create request with file upload
        upload_file = UploadFile(
            file=io.BytesIO(b"mock audio content"),
            filename="interview_recording.mp3"
        )

        request = TranscriptionRequest(
            file=upload_file,
            language_code="en-US",
            media_format="mp3",
            user="journalist_user"
        )

        # 2. Simulate job submission response
        job_id = "transcription_job_2023_10_01_001"
        response = TranscriptionJobResponse(job_id=job_id)

        # 3. Verify data consistency
        assert request.file == upload_file
        assert request.language_code == "en-US"
        assert response.job_id == job_id

    def test_form_request_with_response(self):
        """Test form-based request with job response."""
        # Simulate form submission
        file_url = "s3://audio-uploads/user123/recording.wav"

        request = TranscriptionRequest.as_form(
            file_url=file_url,
            language_code="es-MX",
            user="mobile_user_456"
        )

        # Simulate job creation
        job_id = "batch_transcribe_es_mx_789"
        response = TranscriptionJobResponse(job_id=job_id)

        assert isinstance(request.file, str)
        assert request.file == file_url
        assert response.job_id == job_id

    def test_serialization_roundtrip_with_string_file(self):
        """Test serialization roundtrip with string file."""
        original_request = TranscriptionRequest(
            file="s3://bucket/audio.mp3",
            language_code="fr-CA",
            media_format="mp3",
            user="canadian_user"
        )

        # Serialize and deserialize
        json_data = original_request.model_dump()
        recreated_request = TranscriptionRequest(**json_data)

        assert recreated_request.file == original_request.file
        assert recreated_request.language_code == original_request.language_code
        assert recreated_request.media_format == original_request.media_format
        assert recreated_request.user == original_request.user

    def test_response_serialization_roundtrip(self):
        """Test response serialization roundtrip."""
        original_response = TranscriptionJobResponse(
            job_id="roundtrip_test_job_12345"
        )

        # Serialize and deserialize
        json_str = original_response.model_dump_json()
        import json
        json_data = json.loads(json_str)
        recreated_response = TranscriptionJobResponse(**json_data)

        assert recreated_response.job_id == original_response.job_id

    def test_realistic_transcription_workflow(self):
        """Test realistic transcription workflow scenarios."""
        scenarios = [
            {
                "name": "Mobile app upload",
                "file": UploadFile(
                    file=io.BytesIO(b"mobile audio"),
                    filename="voice_memo_2023.m4a"
                ),
                "language": "en-US",
                "format": "m4a",
                "user": "mobile_user_123"
            },
            {
                "name": "Web form with URL",
                "file": "https://cdn.example.com/podcast/episode1.mp3",
                "language": "en-GB",
                "format": "mp3",
                "user": "podcast_editor"
            },
            {
                "name": "Batch processing",
                "file": "s3://enterprise-audio/meetings/2023-10-01/board-meeting.wav",
                "language": "en-US",
                "format": "wav",
                "user": "enterprise_user_corp"
            }
        ]

        for scenario in scenarios:
            if isinstance(scenario["file"], str):
                request = TranscriptionRequest(
                    file=scenario["file"],
                    language_code=scenario["language"],
                    media_format=scenario["format"],
                    user=scenario["user"]
                )
            else:
                request = TranscriptionRequest(
                    file=scenario["file"],
                    language_code=scenario["language"],
                    media_format=scenario["format"],
                    user=scenario["user"]
                )

            # Simulate job response
            job_id = f"job_{scenario['user']}_{hash(scenario['name']) % 10000}"
            response = TranscriptionJobResponse(job_id=job_id)

            # Verify everything works
            assert request.file == scenario["file"]
            assert request.language_code == scenario["language"]
            assert request.media_format == scenario["format"]
            assert request.user == scenario["user"]
            assert response.job_id == job_id
