"""Tests for common.models.tags module."""

import uuid
from datetime import datetime

import pytest
from pydantic import ValidationError

from common.models.tags import (
    LiteLLMDailyTagSpend,
    TagUsageSummary,
    TagUsageDetail,
    TagUsageByDate,
    TagUsageByModel,
    TagUsageResponse
)


class TestLiteLLMDailyTagSpendSQLAlchemyModel:
    """Test cases for LiteLLM_DailyTagSpend SQLAlchemy model."""

    def test_model_table_name(self):
        """Test that the model has the correct table name."""
        assert LiteLLMDailyTagSpend.__tablename__ == "LiteLLM_DailyTagSpend"

    def test_model_table_schema(self):
        """Test that the model has the correct schema."""
        assert LiteLLMDailyTagSpend.__table_args__["schema"] == "public"

    def test_model_has_required_attributes(self):
        """Test that the model has all required database attributes."""
        # Test that the model can be instantiated
        instance = LiteLLMDailyTagSpend()

        # Check that it has the expected SQLAlchemy attributes
        assert hasattr(instance, '__table__')
        assert hasattr(instance, '__tablename__')
        assert hasattr(instance, '__table_args__')

    def test_model_column_names(self):
        """Test that all expected columns are present."""
        expected_columns = {
            'id', 'tag', 'date', 'api_key', 'model', 'model_group',
            'custom_llm_provider', 'prompt_tokens', 'completion_tokens',
            'cache_read_input_tokens', 'cache_creation_input_tokens',
            'api_requests', 'successful_requests', 'failed_requests',
            'created_at', 'updated_at', 'spend'
        }

        actual_columns = set(LiteLLMDailyTagSpend.__table__.columns.keys())
        assert actual_columns == expected_columns

    def test_model_primary_key(self):
        """Test the primary key configuration."""
        id_column = LiteLLMDailyTagSpend.__table__.columns['id']
        assert id_column.primary_key

    def test_model_column_types(self):
        """Test column type configurations."""
        columns = LiteLLMDailyTagSpend.__table__.columns

        # Check TEXT columns
        text_columns = ['tag', 'date', 'api_key', 'model', 'model_group', 'custom_llm_provider']
        for col_name in text_columns:
            assert col_name in columns

        # Check INTEGER columns
        int_columns = [
            'prompt_tokens', 'completion_tokens', 'cache_read_input_tokens',
            'cache_creation_input_tokens', 'api_requests', 'successful_requests',
            'failed_requests'
        ]
        for col_name in int_columns:
            assert col_name in columns

        # Check TIMESTAMP columns
        timestamp_columns = ['created_at', 'updated_at']
        for col_name in timestamp_columns:
            assert col_name in columns

        # Check spend column exists
        assert 'spend' in columns


class TestTagUsageSummary:
    """Test cases for TagUsageSummary Pydantic model."""

    def test_tag_usage_summary_creation(self):
        """Test creating TagUsageSummary with valid data."""
        summary = TagUsageSummary(
            tag="production",
            total_spend=125.50,
            total_requests=1000,
            total_tokens=50000,
            success_rate=99.5,
            date_range="2023-10-01 to 2023-10-07"
        )

        assert summary.tag == "production"
        assert summary.total_spend == 125.50
        assert summary.total_requests == 1000
        assert summary.total_tokens == 50000
        assert summary.success_rate == 99.5
        assert summary.date_range == "2023-10-01 to 2023-10-07"

    def test_tag_usage_summary_missing_required_fields(self):
        """Test TagUsageSummary with missing required fields."""
        with pytest.raises(ValidationError):
            TagUsageSummary(
                total_spend=100.0,
                total_requests=1000,
                total_tokens=50000,
                success_rate=99.0,
                date_range="2023-10-01 to 2023-10-07"
            )  # Missing tag

    def test_tag_usage_summary_field_types(self):
        """Test TagUsageSummary field type validation."""
        summary = TagUsageSummary(
            tag="test",
            total_spend=0.0,
            total_requests=0,
            total_tokens=0,
            success_rate=0.0,
            date_range="single day"
        )

        assert isinstance(summary.tag, str)
        assert isinstance(summary.total_spend, float)
        assert isinstance(summary.total_requests, int)
        assert isinstance(summary.total_tokens, int)
        assert isinstance(summary.success_rate, float)
        assert isinstance(summary.date_range, str)

    def test_tag_usage_summary_edge_values(self):
        """Test TagUsageSummary with edge case values."""
        # Test with zero values
        summary = TagUsageSummary(
            tag="",
            total_spend=0.0,
            total_requests=0,
            total_tokens=0,
            success_rate=0.0,
            date_range=""
        )

        assert summary.total_spend == 0.0
        assert summary.total_requests == 0
        assert summary.success_rate == 0.0

        # Test with large values
        summary.total_spend = 999999.99
        summary.total_requests = 1000000
        summary.total_tokens = 10000000
        summary.success_rate = 100.0

        assert summary.total_spend == 999999.99
        assert summary.total_requests == 1000000
        assert summary.total_tokens == 10000000
        assert summary.success_rate == 100.0


class TestTagUsageDetail:
    """Test cases for TagUsageDetail Pydantic model."""

    def test_tag_usage_detail_creation(self):
        """Test creating TagUsageDetail with valid data."""
        test_id = uuid.uuid4()
        test_datetime = datetime(2023, 10, 1, 12, 0, 0)

        detail = TagUsageDetail(
            id=test_id,
            tag="production",
            date="2023-10-01",
            api_key="sk-test123",
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=1000,
            completion_tokens=500,
            cache_read_input_tokens=100,
            cache_creation_input_tokens=50,
            spend=25.75,
            api_requests=10,
            successful_requests=9,
            failed_requests=1,
            created_at=test_datetime,
            updated_at=test_datetime
        )

        assert detail.id == test_id
        assert detail.tag == "production"
        assert detail.date == "2023-10-01"
        assert detail.api_key == "sk-test123"
        assert detail.model == "gpt-4"
        assert detail.model_group == "openai"
        assert detail.custom_llm_provider == "openai"
        assert detail.prompt_tokens == 1000
        assert detail.completion_tokens == 500
        assert detail.cache_read_input_tokens == 100
        assert detail.cache_creation_input_tokens == 50
        assert detail.spend == 25.75
        assert detail.api_requests == 10
        assert detail.successful_requests == 9
        assert detail.failed_requests == 1
        assert detail.created_at == test_datetime
        assert detail.updated_at == test_datetime

    def test_tag_usage_detail_uuid_validation(self):
        """Test TagUsageDetail UUID validation."""
        test_datetime = datetime(2023, 10, 1, 12, 0, 0)

        # Valid UUID string
        uuid_str = "12345678-1234-5678-9abc-123456789012"
        detail = TagUsageDetail(
            id=uuid_str,
            tag="test",
            date="2023-10-01",
            api_key="sk-test",
            model="gpt-3.5-turbo",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=100,
            completion_tokens=50,
            cache_read_input_tokens=10,
            cache_creation_input_tokens=5,
            spend=5.25,
            api_requests=1,
            successful_requests=1,
            failed_requests=0,
            created_at=test_datetime,
            updated_at=test_datetime
        )

        assert detail.id == uuid.UUID(uuid_str)

    def test_tag_usage_detail_json_serialization(self):
        """Test TagUsageDetail JSON serialization."""
        test_id = uuid.uuid4()
        test_datetime = datetime(2023, 10, 1, 12, 0, 0)

        detail = TagUsageDetail(
            id=test_id,
            tag="json-test",
            date="2023-10-01",
            api_key="sk-json123",
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            prompt_tokens=500,
            completion_tokens=250,
            cache_read_input_tokens=50,
            cache_creation_input_tokens=25,
            spend=12.50,
            api_requests=5,
            successful_requests=5,
            failed_requests=0,
            created_at=test_datetime,
            updated_at=test_datetime
        )

        json_data = detail.model_dump()
        assert json_data["id"] == test_id
        assert json_data["tag"] == "json-test"
        assert json_data["spend"] == 12.50
        assert json_data["prompt_tokens"] == 500


class TestTagUsageByDate:
    """Test cases for TagUsageByDate Pydantic model."""

    def test_tag_usage_by_date_creation(self):
        """Test creating TagUsageByDate with valid data."""
        by_date = TagUsageByDate(
            date="2023-10-01",
            spend=50.25,
            api_requests=100,
            total_tokens=25000,
            success_rate=98.5
        )

        assert by_date.date == "2023-10-01"
        assert by_date.spend == 50.25
        assert by_date.api_requests == 100
        assert by_date.total_tokens == 25000
        assert by_date.success_rate == 98.5

    def test_tag_usage_by_date_edge_cases(self):
        """Test TagUsageByDate with edge case values."""
        # Zero values
        by_date = TagUsageByDate(
            date="2023-10-02",
            spend=0.0,
            api_requests=0,
            total_tokens=0,
            success_rate=0.0
        )

        assert by_date.spend == 0.0
        assert by_date.api_requests == 0
        assert by_date.total_tokens == 0
        assert by_date.success_rate == 0.0


class TestTagUsageByModel:
    """Test cases for TagUsageByModel Pydantic model."""

    def test_tag_usage_by_model_creation(self):
        """Test creating TagUsageByModel with valid data."""
        by_model = TagUsageByModel(
            model="gpt-4",
            model_group="openai",
            custom_llm_provider="openai",
            total_spend=150.75,
            total_requests=500,
            total_tokens=100000,
            success_rate=99.8
        )

        assert by_model.model == "gpt-4"
        assert by_model.model_group == "openai"
        assert by_model.custom_llm_provider == "openai"
        assert by_model.total_spend == 150.75
        assert by_model.total_requests == 500
        assert by_model.total_tokens == 100000
        assert by_model.success_rate == 99.8

    def test_tag_usage_by_model_different_providers(self):
        """Test TagUsageByModel with different providers."""
        models = [
            {
                "model": "gpt-3.5-turbo",
                "model_group": "openai",
                "custom_llm_provider": "openai"
            },
            {
                "model": "claude-2",
                "model_group": "anthropic",
                "custom_llm_provider": "anthropic"
            },
            {
                "model": "llama-2-70b",
                "model_group": "meta",
                "custom_llm_provider": "together"
            }
        ]

        for model_info in models:
            by_model = TagUsageByModel(
                model=model_info["model"],
                model_group=model_info["model_group"],
                custom_llm_provider=model_info["custom_llm_provider"],
                total_spend=100.0,
                total_requests=100,
                total_tokens=10000,
                success_rate=95.0
            )

            assert by_model.model == model_info["model"]
            assert by_model.model_group == model_info["model_group"]
            assert by_model.custom_llm_provider == model_info["custom_llm_provider"]


class TestTagUsageResponse:
    """Test cases for TagUsageResponse Pydantic model."""

    def test_tag_usage_response_creation(self):
        """Test creating TagUsageResponse with valid data."""
        summary = TagUsageSummary(
            tag="test-tag",
            total_spend=200.50,
            total_requests=1000,
            total_tokens=50000,
            success_rate=99.2,
            date_range="2023-10-01 to 2023-10-07"
        )

        daily_data = [
            TagUsageByDate(
                date="2023-10-01",
                spend=50.0,
                api_requests=250,
                total_tokens=12500,
                success_rate=99.0
            ),
            TagUsageByDate(
                date="2023-10-02",
                spend=75.25,
                api_requests=375,
                total_tokens=18750,
                success_rate=99.5
            )
        ]

        model_breakdown = [
            TagUsageByModel(
                model="gpt-4",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=150.0,
                total_requests=750,
                total_tokens=37500,
                success_rate=99.8
            ),
            TagUsageByModel(
                model="gpt-3.5-turbo",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=50.5,
                total_requests=250,
                total_tokens=12500,
                success_rate=98.0
            )
        ]

        response = TagUsageResponse(
            tag="test-tag",
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=625
        )

        assert response.tag == "test-tag"
        assert response.summary == summary
        assert response.daily_data == daily_data
        assert response.model_breakdown == model_breakdown
        assert response.total_records == 625

    def test_tag_usage_response_empty_lists(self):
        """Test TagUsageResponse with empty lists."""
        summary = TagUsageSummary(
            tag="empty-test",
            total_spend=0.0,
            total_requests=0,
            total_tokens=0,
            success_rate=0.0,
            date_range="No data"
        )

        response = TagUsageResponse(
            tag="empty-test",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0
        )

        assert response.tag == "empty-test"
        assert response.summary == summary
        assert response.daily_data == []
        assert response.model_breakdown == []
        assert response.total_records == 0

    def test_tag_usage_response_json_serialization(self):
        """Test TagUsageResponse JSON serialization."""
        summary = TagUsageSummary(
            tag="json-test",
            total_spend=100.0,
            total_requests=500,
            total_tokens=25000,
            success_rate=99.0,
            date_range="2023-10-01"
        )

        response = TagUsageResponse(
            tag="json-test",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0
        )

        json_data = response.model_dump()
        assert json_data["tag"] == "json-test"
        assert json_data["summary"]["tag"] == "json-test"
        assert json_data["summary"]["total_spend"] == 100.0
        assert json_data["daily_data"] == []
        assert json_data["model_breakdown"] == []
        assert json_data["total_records"] == 0


class TestTagsModelsIntegration:
    """Integration tests for tag models."""

    def test_realistic_tag_usage_workflow(self):
        """Test realistic tag usage workflow with all models."""
        # Create summary
        summary = TagUsageSummary(
            tag="production-api",
            total_spend=1250.75,
            total_requests=5000,
            total_tokens=250000,
            success_rate=99.8,
            date_range="2023-10-01 to 2023-10-07"
        )

        # Create daily breakdown that adds up to summary total (1250.75)
        daily_spends = [171.18, 176.43, 181.68, 186.93, 192.18, 197.43, 144.92]  # Total = 1250.75
        daily_data = []
        for day in range(1, 8):  # 7 days
            daily_data.append(TagUsageByDate(
                date=f"2023-10-0{day}",
                spend=daily_spends[day - 1],
                api_requests=500 + day * 100,
                total_tokens=25000 + day * 5000,
                success_rate=99.0 + day * 0.1
            ))

        # Create model breakdown
        model_breakdown = [
            TagUsageByModel(
                model="gpt-4",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=875.52,
                total_requests=3500,
                total_tokens=175000,
                success_rate=99.9
            ),
            TagUsageByModel(
                model="gpt-3.5-turbo",
                model_group="openai",
                custom_llm_provider="openai",
                total_spend=375.23,
                total_requests=1500,
                total_tokens=75000,
                success_rate=99.5
            )
        ]

        # Create complete response
        response = TagUsageResponse(
            tag="production-api",
            summary=summary,
            daily_data=daily_data,
            model_breakdown=model_breakdown,
            total_records=5000
        )

        # Verify the complete response
        assert response.tag == "production-api"
        assert len(response.daily_data) == 7
        assert len(response.model_breakdown) == 2
        assert response.total_records == 5000

        # Verify totals match
        total_daily_spend = sum(day.spend for day in daily_data)
        total_model_spend = sum(model.total_spend for model in model_breakdown)

        # Should be approximately equal (allowing for rounding)
        assert abs(total_daily_spend - summary.total_spend) < 1.0
        assert abs(total_model_spend - summary.total_spend) < 1.0

    def test_model_serialization_consistency(self):
        """Test that all models serialize consistently."""
        # Test each model's serialization
        summary = TagUsageSummary(
            tag="consistency-test",
            total_spend=100.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=99.0,
            date_range="test-range"
        )

        by_date = TagUsageByDate(
            date="2023-10-01",
            spend=25.0,
            api_requests=250,
            total_tokens=12500,
            success_rate=99.0
        )

        by_model = TagUsageByModel(
            model="test-model",
            model_group="test-group",
            custom_llm_provider="test-provider",
            total_spend=100.0,
            total_requests=1000,
            total_tokens=50000,
            success_rate=99.0
        )

        # All should serialize without errors
        summary_json = summary.model_dump()
        by_date_json = by_date.model_dump()
        by_model_json = by_model.model_dump()

        assert summary_json["tag"] == "consistency-test"
        assert by_date_json["date"] == "2023-10-01"
        assert by_model_json["model"] == "test-model"

    def test_edge_case_handling(self):
        """Test edge case handling across all models."""
        # Test with minimal/zero values
        summary = TagUsageSummary(
            tag="",
            total_spend=0.0,
            total_requests=0,
            total_tokens=0,
            success_rate=0.0,
            date_range=""
        )

        response = TagUsageResponse(
            tag="",
            summary=summary,
            daily_data=[],
            model_breakdown=[],
            total_records=0
        )

        assert response.tag == ""
        assert response.summary.total_spend == 0.0
        assert len(response.daily_data) == 0
        assert len(response.model_breakdown) == 0
        assert response.total_records == 0
