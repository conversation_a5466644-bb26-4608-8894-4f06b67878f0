"""FastAPI-related test fixtures."""

import uuid
from typing import Any, Dict, Generator, Callable, Optional

import pytest
from fastapi import Request, Response
from fastapi.testclient import TestClient


# FastAPI client
@pytest.fixture
def fastapi_client() -> Generator[TestClient, None, None]:
    """Create a FastAPI test client with proper state management."""
    from api.main import app

    class CustomTestClient(TestClient):
        def __init__(self, app: Any, state: Optional[Dict[str, Any]] = None):
            self._mystate = state or {
                "client_guid": str(uuid.uuid4()),
                "user_guid": str(uuid.uuid4()),
            }

            # Reset middleware stack to avoid conflicts
            app.user_middleware = []
            app.middleware_stack = None

            @app.middleware("http")  # type: ignore[misc]
            async def add_state(request: Request, call_next: Callable[[Request], Any]) -> Response:
                for key, value in self.state.items():
                    setattr(request.state, key, value)
                return await call_next(request)

            # Initialize the TestClient
            super().__init__(app, base_url="http://testserver/api/encore/v1")

        @property
        def state(self) -> Dict[str, Any]:
            return self._mystate

    with CustomTestClient(app) as client:
        yield client
