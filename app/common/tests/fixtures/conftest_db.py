"""Database-related test fixtures."""

from typing import Any, Generator

import pytest
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession

from common.services import JobStore, PostgresChatStore, AgentStore, PromptStore


# Database fixtures
@pytest.fixture(scope="session")
def test_db_url() -> str:
    """Test database URL for SQLite in-memory database."""
    return "sqlite:///:memory:"


@pytest.fixture(scope="session")
def test_async_db_url() -> str:
    """Test async database URL for SQLite in-memory database."""
    return "sqlite+aiosqlite:///:memory:"


@pytest.fixture
def db_engine(test_db_url: str) -> Generator[Engine, None, None]:
    """Create a test database engine."""
    engine = create_engine(test_db_url, echo=False)
    yield engine
    engine.dispose()


@pytest.fixture
def async_db_engine(test_async_db_url: str) -> Generator[Any, None, None]:
    """Create a test async database engine."""
    engine = create_async_engine(test_async_db_url, echo=False)
    yield engine
    engine.sync_engine.dispose()


@pytest.fixture
def db_session(db_engine: Engine) -> Generator[Any, None, None]:
    """Create a test database session."""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=db_engine)
    yield SessionLocal


@pytest.fixture
def async_db_session(async_db_engine: Any) -> Generator[Any, None, None]:
    """Create a test async database session."""
    AsyncSessionLocal = sessionmaker(
        bind=async_db_engine,
        class_=AsyncSession,
        autocommit=False,
        autoflush=False,
    )
    yield AsyncSessionLocal


# Service fixtures
@pytest.fixture
def job_store(db_session: Any, async_db_session: Any) -> JobStore:
    """Create a JobStore instance for testing."""
    return JobStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_batch_jobs",
        schema_name="public",
        use_jsonb=False,
    )


@pytest.fixture
def chat_store(db_session: Any, async_db_session: Any) -> PostgresChatStore:
    """Create a PostgresChatStore instance for testing."""
    return PostgresChatStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_chatstore",
        schema_name="public",
        use_jsonb=False,
    )


@pytest.fixture
def agent_store(db_session: Any, async_db_session: Any) -> AgentStore:
    """Create an AgentStore instance for testing."""
    return AgentStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_agents",
        schema_name="public",
    )


@pytest.fixture
def prompt_store(db_session: Any, async_db_session: Any) -> PromptStore:
    """Create a PromptStore instance for testing."""
    return PromptStore(
        session=db_session,
        async_session=async_db_session,
        table_name="test_prompts",
        schema_name="public",
    )
