"""Test data-related fixtures."""

import uuid
from datetime import datetime
from typing import Dict, Any

import pytest

from common.enums import BatchJobStatusEnum


# Utility fixtures
@pytest.fixture
def mock_current_time() -> datetime:
    """Mock the current time for consistent testing."""
    return datetime(2023, 10, 1, 12, 0, 0)


@pytest.fixture(scope="session")
def json_test_data() -> Dict[str, str]:
    """Sample JSON data for parser testing."""
    return {
        "valid_json": '{"name": "test", "value": 123}',
        "json_in_markdown": '```json\n{"name": "test", "value": 123}\n```',
        "json_with_header": 'Here is your result:\n{"name": "test", "value": 123}',
        "invalid_json": '{"name": "test", "value":}',
        "malformed_json": '{"name": "test" "value": 123}',
    }


# Test data fixtures
@pytest.fixture
def sample_client_guid() -> uuid.UUID:
    """Sample client GUID for testing."""
    return uuid.UUID("12345678-1234-5678-9abc-123456789012")


@pytest.fixture
def sample_user_guid() -> uuid.UUID:
    """Sample user GUID for testing."""
    return uuid.UUID("*************-8765-cba9-************")


@pytest.fixture
def sample_job_params() -> Dict[str, Any]:
    """Sample job parameters for testing."""
    return {
        "param1": "value1",
        "param2": "value2",
        "param3": 123,
        "param4": True
    }


@pytest.fixture
def fake_batch_job_id() -> str:
    """Generate a fake batch job ID."""
    return f"batch-{uuid.uuid4()}"


@pytest.fixture
def fake_chat_session_id() -> str:
    """Generate a fake chat session ID."""
    return f"chat-{uuid.uuid4()}"


@pytest.fixture
def fake_chat_message_id() -> str:
    """Generate a fake chat message ID."""
    return f"msg-{uuid.uuid4()}"


@pytest.fixture
def fake_batch_job(
    fake_batch_job_id: str,
    sample_client_guid: uuid.UUID,
    sample_user_guid: uuid.UUID,
    sample_job_params: Dict[str, Any]
) -> Dict[str, Any]:
    """Fixture to generate a fake batch job."""
    return {
        "job_id": fake_batch_job_id,
        "status": BatchJobStatusEnum.PENDING.value,
        "client_guid": str(sample_client_guid),
        "user_guid": str(sample_user_guid),
        "job_params": sample_job_params,
        "results": None,
        "processed_count": 0,
        "total_count": 100,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:00:00Z",
        "started_at": None,
        "completed_at": None,
    }


@pytest.fixture
def fake_batch_job_in_progress(
    fake_batch_job_id: str,
    sample_client_guid: uuid.UUID,
    sample_user_guid: uuid.UUID,
    sample_job_params: Dict[str, Any]
) -> Dict[str, Any]:
    """Fixture to generate a fake job in progress."""
    return {
        "job_id": fake_batch_job_id,
        "status": BatchJobStatusEnum.PROCESSING.value,
        "client_guid": str(sample_client_guid),
        "user_guid": str(sample_user_guid),
        "job_params": sample_job_params,
        "results": None,
        "processed_count": 5,
        "total_count": 10,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:05:00Z",
        "started_at": "2023-10-01T00:01:00Z",
        "completed_at": None,
    }


@pytest.fixture
def fake_batch_job_in_completed_state(
    fake_batch_job_id: str,
    sample_client_guid: uuid.UUID,
    sample_user_guid: uuid.UUID,
    sample_job_params: Dict[str, Any]
) -> Dict[str, Any]:
    """Fixture to generate a fake job in completed state."""
    return {
        "job_id": fake_batch_job_id,
        "status": BatchJobStatusEnum.COMPLETED.value,
        "client_guid": str(sample_client_guid),
        "user_guid": str(sample_user_guid),
        "job_params": sample_job_params,
        "results": {
            "output_location": "s3://test-bucket/results/batch-123/",
            "summary": {"processed": 10, "failed": 0}
        },
        "processed_count": 10,
        "total_count": 10,
        "created_at": "2023-10-01T00:00:00Z",
        "updated_at": "2023-10-01T00:10:00Z",
        "started_at": "2023-10-01T00:01:00Z",
        "completed_at": "2023-10-01T00:10:00Z",
    }
