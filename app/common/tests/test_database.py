"""Tests for common.database module."""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import SQLAlchemy<PERSON>rror, OperationalError
from sqlalchemy.orm import Session

from common.database import get_db_session


class TestGetDbSession:
    """Test cases for get_db_session function."""

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_success(self, mock_sessionmaker, mock_create_engine, mock_settings):  # type: ignore
        """Test successful database session creation and cleanup."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"  # type: ignore
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        session = next(session_generator)

        # Assert
        mock_create_engine.assert_called_once_with("postgresql://user:pass@localhost/test")
        mock_sessionmaker.assert_called_once_with(autocommit=False, autoflush=False, bind=mock_engine)
        mock_session_class.assert_called_once()
        assert session == mock_session

        # Test cleanup
        try:
            next(session_generator)
        except StopIteration:
            pass
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_with_context_manager(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test database session used with context manager."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act & Assert
        session_generator = get_db_session()
        with session_generator as session:
            assert session == mock_session
            mock_session.close.assert_not_called()  # Should not be closed yet

        # Session should be closed after context manager exits
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_engine_creation_error(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test error handling when engine creation fails."""
        # Arrange
        mock_settings.database.connection_string = "invalid://connection/string"
        mock_create_engine.side_effect = SQLAlchemyError("Invalid connection string")

        # Act & Assert
        with pytest.raises(SQLAlchemyError, match="Invalid connection string"):
            session_generator = get_db_session()
            next(session_generator)

        mock_create_engine.assert_called_once_with("invalid://connection/string")
        mock_sessionmaker.assert_not_called()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_session_creation_error(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test error handling when session creation fails."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session_class.side_effect = OperationalError("Connection failed", None, None)

        # Act & Assert
        with pytest.raises(OperationalError, match="Connection failed"):
            session_generator = get_db_session()
            next(session_generator)

        mock_create_engine.assert_called_once_with("postgresql://user:pass@localhost/test")
        mock_sessionmaker.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_session_close_error(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test error handling when session close fails."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session
        mock_session.close.side_effect = SQLAlchemyError("Close failed")

        # Act - session should still be yielded despite close error
        session_generator = get_db_session()
        session = next(session_generator)
        assert session == mock_session

        # Assert - close error should be raised when generator completes
        with pytest.raises(SQLAlchemyError, match="Close failed"):
            try:
                next(session_generator)
            except StopIteration:
                pass

        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_empty_connection_string(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test behavior with empty connection string."""
        # Arrange
        mock_settings.database.connection_string = ""
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        session = next(session_generator)

        # Assert
        mock_create_engine.assert_called_once_with("")
        assert session == mock_session

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_none_connection_string(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test behavior with None connection string."""
        # Arrange
        mock_settings.database.connection_string = None
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()
        session = next(session_generator)

        # Assert
        mock_create_engine.assert_called_once_with(None)
        assert session == mock_session

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_multiple_calls(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test multiple calls to get_db_session create separate sessions."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session1 = Mock(spec=Session)
        mock_session2 = Mock(spec=Session)
        mock_session_class.side_effect = [mock_session1, mock_session2]

        # Act
        session_generator1 = get_db_session()
        session1 = next(session_generator1)

        session_generator2 = get_db_session()
        session2 = next(session_generator2)

        # Assert
        assert session1 == mock_session1
        assert session2 == mock_session2
        assert session1 != session2
        assert mock_create_engine.call_count == 2
        assert mock_session_class.call_count == 2

        # Cleanup
        try:
            next(session_generator1)
        except StopIteration:
            pass
        try:
            next(session_generator2)
        except StopIteration:
            pass

        mock_session1.close.assert_called_once()
        mock_session2.close.assert_called_once()

    @patch('common.database.settings')
    @patch('common.database.create_engine')
    @patch('common.database.sessionmaker')
    def test_get_db_session_generator_protocol(self, mock_sessionmaker, mock_create_engine, mock_settings):
        """Test that get_db_session follows proper generator protocol."""
        # Arrange
        mock_settings.database.connection_string = "postgresql://user:pass@localhost/test"
        mock_engine = Mock()
        mock_create_engine.return_value = mock_engine
        mock_session_class = Mock()
        mock_sessionmaker.return_value = mock_session_class
        mock_session = Mock(spec=Session)
        mock_session_class.return_value = mock_session

        # Act
        session_generator = get_db_session()

        # Test generator yields exactly one value
        session = next(session_generator)
        assert session == mock_session

        # Test generator raises StopIteration after yielding
        with pytest.raises(StopIteration):
            next(session_generator)

        # Verify session was closed
        mock_session.close.assert_called_once()

    @patch('common.database.settings')
    def test_settings_import_and_usage(self, mock_settings):
        """Test that settings is properly imported and used."""
        # Arrange
        mock_settings.database.connection_string = "test://connection"

        # Act
        with patch('common.database.create_engine') as mock_create_engine, \
                patch('common.database.sessionmaker') as mock_sessionmaker:

            mock_engine = Mock()
            mock_create_engine.return_value = mock_engine
            mock_session_class = Mock()
            mock_sessionmaker.return_value = mock_session_class
            mock_session = Mock(spec=Session)
            mock_session_class.return_value = mock_session

            session_generator = get_db_session()
            next(session_generator)

        # Assert
        mock_create_engine.assert_called_once_with("test://connection")
