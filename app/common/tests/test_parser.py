"""Tests for common.parser module."""

import json
import pytest
from unittest.mock import patch

from common.parser import extract_and_parse_json


class TestExtractAndParseJson:
    """Test cases for extract_and_parse_json function."""

    def test_parse_markdown_json_code_block(self):
        """Test parsing JSON from markdown code block."""
        content = '```json\n{"name": "test", "value": 123}\n```'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_markdown_code_block_without_language(self):
        """Test parsing JSON from markdown code block without language specifier."""
        content = '```\n{"name": "test", "value": 123}\n```'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_markdown_with_extra_whitespace(self):
        """Test parsing JSON from markdown with extra whitespace."""
        content = '```json\n  \n  {"name": "test", "value": 123}  \n  \n```'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_json_with_header_text_dict(self):
        """Test parsing JSON with header text (dict pattern)."""
        content = 'Here is the JSON you are looking for\n{"name": "test", "value": 123}'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_json_with_header_text_list(self):
        """Test parsing JSON with header text (list pattern)."""
        content = 'Here is your result:\n[{"name": "test"}, {"value": 123}]'
        result = extract_and_parse_json(content)
        expected = [{"name": "test"}, {"value": 123}]
        assert result == expected

    def test_parse_json_with_multiline_header(self):
        """Test parsing JSON with multiline header text."""
        content = '''Here is your result:
        This is the data you requested:
        {"name": "test", "value": 123}
        End of data.'''
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_plain_json_dict(self):
        """Test parsing plain JSON dict content."""
        content = '{"name": "test", "value": 123}'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_plain_json_list(self):
        """Test parsing plain JSON list content."""
        content = '[{"name": "test"}, {"value": 123}]'
        result = extract_and_parse_json(content)
        expected = [{"name": "test"}, {"value": 123}]
        assert result == expected

    def test_parse_json_with_extra_whitespace(self):
        """Test parsing JSON content with extra whitespace."""
        content = '  \n  {"name": "test", "value": 123}  \n  '
        result = extract_and_parse_json(content)
        expected = {"name": "test", "value": 123}
        assert result == expected

    def test_parse_empty_dict(self):
        """Test parsing empty JSON dict."""
        content = '{}'
        result = extract_and_parse_json(content)
        expected = {}
        assert result == expected

    def test_parse_empty_list(self):
        """Test parsing empty JSON list."""
        content = '[]'
        result = extract_and_parse_json(content)
        expected = []
        assert result == expected

    def test_parse_nested_json(self):
        """Test parsing complex nested JSON."""
        content = '''```json
        {
            "data": {
                "users": [
                    {"id": 1, "name": "Alice"},
                    {"id": 2, "name": "Bob"}
                ],
                "metadata": {
                    "total": 2,
                    "page": 1
                }
            }
        }
        ```'''
        result = extract_and_parse_json(content)
        expected = {
            "data": {
                "users": [
                    {"id": 1, "name": "Alice"},
                    {"id": 2, "name": "Bob"}
                ],
                "metadata": {
                    "total": 2,
                    "page": 1
                }
            }
        }
        assert result == expected

    def test_parse_json_with_special_characters(self):
        """Test parsing JSON with special characters."""
        content = '{"message": "Hello\\nWorld!", "emoji": "🚀", "unicode": "\\u0048\\u0065\\u006c\\u006c\\u006f"}'
        result = extract_and_parse_json(content)
        expected = {"message": "Hello\nWorld!", "emoji": "🚀", "unicode": "Hello"}
        assert result == expected

    def test_parse_json_with_null_values(self):
        """Test parsing JSON with null values."""
        content = '{"name": null, "value": 123, "active": true, "data": false}'
        result = extract_and_parse_json(content)
        expected = {"name": None, "value": 123, "active": True, "data": False}
        assert result == expected

    def test_parse_json_with_numbers(self):
        """Test parsing JSON with various number formats."""
        content = '{"integer": 42, "float": 3.14, "negative": -100, "scientific": 1.23e-4}'
        result = extract_and_parse_json(content)
        expected = {"integer": 42, "float": 3.14, "negative": -100, "scientific": 1.23e-4}
        assert result == expected

    def test_markdown_pattern_priority(self):
        """Test that markdown pattern takes priority over header pattern."""
        content = '''Here is some text with JSON:
        {"header": "json"}
        ```json
        {"markdown": "json"}
        ```
        More text here.'''
        result = extract_and_parse_json(content)
        expected = {"markdown": "json"}
        assert result == expected

    def test_multiple_markdown_blocks(self):
        """Test parsing with multiple markdown blocks (should get first one)."""
        content = '''```json
        {"first": "block"}
        ```
        Some text here.
        ```json
        {"second": "block"}
        ```'''
        result = extract_and_parse_json(content)
        expected = {"first": "block"}
        assert result == expected

    def test_invalid_json_syntax_error(self):
        """Test handling of invalid JSON syntax."""
        content = '{"name": "test", "value":}'  # Missing value
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_malformed_json_missing_quotes(self):
        """Test handling of malformed JSON with missing quotes."""
        content = '{name: "test", "value": 123}'  # Missing quotes on key
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_malformed_json_trailing_comma(self):
        """Test handling of malformed JSON with trailing comma."""
        content = '{"name": "test", "value": 123,}'  # Trailing comma
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_empty_string(self):
        """Test handling of empty string."""
        content = ''
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_whitespace_only(self):
        """Test handling of whitespace-only string."""
        content = '   \n   \t   '
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_non_json_text(self):
        """Test handling of plain text that's not JSON."""
        content = 'This is just plain text without any JSON.'
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_partial_json_in_text(self):
        """Test handling of text with partial JSON that doesn't match patterns."""
        content = 'Here is some data: {"partial"'
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_json_with_extra_characters(self):
        """Test JSON with extra characters - parser should extract valid JSON."""
        content = '{"name": "test"}extra'
        result = extract_and_parse_json(content)
        expected = {"name": "test"}
        assert result == expected

    @patch('common.parser.log')
    def test_json_decode_error_logging(self, mock_log):
        """Test that JSON decode errors are properly logged."""
        content = '{"invalid": json}'

        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

        mock_log.error.assert_called_once()
        args = mock_log.error.call_args[0]
        assert "JSON parsing error:" in args[0]
        assert isinstance(args[1], json.JSONDecodeError)

    @patch('common.parser.log')
    @patch('common.parser.json.loads')
    def test_unexpected_error_logging(self, mock_json_loads, mock_log):
        """Test that unexpected errors during parsing are logged."""
        content = '{"test": "value"}'
        mock_json_loads.side_effect = ValueError("Unexpected error")

        with pytest.raises(ValueError):
            extract_and_parse_json(content)

        mock_log.error.assert_called_once()
        args = mock_log.error.call_args[0]
        assert "Unexpected error while parsing output:" in args[0]
        assert isinstance(args[1], ValueError)

    def test_json_strict_false_parameter(self):
        """Test that JSON parsing uses strict=False parameter."""
        # This content would fail with strict=True but should work with strict=False
        content = '{"name": "test", "control_char": "line1\\nline2"}'
        result = extract_and_parse_json(content)
        expected = {"name": "test", "control_char": "line1\nline2"}
        assert result == expected

    def test_markdown_with_language_variations(self):
        """Test markdown blocks with different language specifiers."""
        test_cases = [
            ('```JSON\n{"test": "uppercase"}\n```', {"test": "uppercase"}),
            ('```Json\n{"test": "mixedcase"}\n```', {"test": "mixedcase"}),
            ('``` json\n{"test": "space_before"}\n```', {"test": "space_before"}),
            ('```json \n{"test": "space_after"}\n```', {"test": "space_after"}),
        ]

        for content, expected in test_cases:
            result = extract_and_parse_json(content)
            assert result == expected

    def test_complex_header_pattern_matching(self):
        """Test complex scenarios for header pattern matching."""
        # Test with multiple JSON-like structures in text
        content = '''Here are some examples:
        This is not JSON: {not valid}
        But this is: {"valid": "json", "number": 42}
        And this too: [1, 2, 3]
        End of examples.'''

        result = extract_and_parse_json(content)
        expected = {"valid": "json", "number": 42}
        assert result == expected

    def test_edge_case_empty_markdown_block(self):
        """Test edge case with empty markdown block."""
        content = '```json\n\n```'
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_edge_case_markdown_with_only_whitespace(self):
        """Test edge case with markdown block containing only whitespace."""
        content = '```json\n   \n   \n```'
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_regex_pattern_boundaries(self):
        """Test that regex patterns correctly handle boundaries."""
        # Test that we don't match partial brackets
        content = 'This text contains { and } and [ and ] but no valid JSON'
        with pytest.raises(json.JSONDecodeError):
            extract_and_parse_json(content)

    def test_multiline_json_in_markdown(self):
        """Test multiline JSON within markdown blocks."""
        content = '''```json
        {
            "multiline": "json",
            "with": {
                "nested": "structure"
            },
            "and": [
                "array",
                "elements"
            ]
        }
        ```'''
        result = extract_and_parse_json(content)
        expected = {
            "multiline": "json",
            "with": {
                "nested": "structure"
            },
            "and": [
                "array",
                "elements"
            ]
        }
        assert result == expected

    def test_json_with_unicode_escapes(self):
        """Test JSON with various Unicode escape sequences."""
        content = r'{"unicode": "\u0041\u0042\u0043", "emoji": "\ud83d\ude80"}'
        result = extract_and_parse_json(content)
        expected = {"unicode": "ABC", "emoji": "🚀"}
        assert result == expected

    def test_large_json_structure(self):
        """Test parsing of large JSON structure."""
        # Create a reasonably large JSON structure
        large_data = {
            "users": [{"id": i, "name": f"User{i}", "active": i % 2 == 0} for i in range(100)],
            "metadata": {"total": 100, "generated": True}
        }
        content = json.dumps(large_data)
        result = extract_and_parse_json(content)
        assert result == large_data
        assert len(result["users"]) == 100  # type: ignore
        assert result["metadata"]["total"] == 100  # type: ignore
