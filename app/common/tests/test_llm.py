"""Concise tests for common.models.llm module."""

import pytest
from pydantic import ValidationError

from common.models.llm import CompletionRequest, ChatCompletionRequest


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_minimal_creation(self):
        """Test creating CompletionRequest with minimal required fields."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Hello, world!"
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.prompt == "Hello, world!"
        assert request.max_tokens is None
        assert request.temperature is None

    def test_all_fields(self):
        """Test CompletionRequest with all fields populated."""
        request = CompletionRequest(
            model="gpt-4",
            prompt="Complete this text",
            max_tokens=150,
            temperature=0.7,
            top_p=0.9,
            n=1,
            stream=False,
            logprobs=None,
            echo=False,
            stop=None,
            presence_penalty=0.0,
            frequency_penalty=0.0,
            best_of=1,
            logit_bias=None,
            user="test_user",
            files=["s3://bucket/doc.pdf"]
        )

        assert request.model == "gpt-4"
        assert request.prompt == "Complete this text"
        assert request.max_tokens == 150
        assert request.temperature == 0.7
        assert request.top_p == 0.9
        assert request.files == ["s3://bucket/doc.pdf"]
        assert request.user == "test_user"

    def test_missing_required_fields(self):
        """Test CompletionRequest validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            CompletionRequest(prompt="Hello")  # Missing model

        assert "model" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            CompletionRequest(model="gpt-3.5-turbo")  # Missing prompt

        assert "prompt" in str(exc_info.value)

    def test_json_serialization(self):
        """Test CompletionRequest JSON serialization."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test prompt",
            max_tokens=100,
            temperature=0.5
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["prompt"] == "Test prompt"
        assert json_data["max_tokens"] == 100
        assert json_data["temperature"] == 0.5

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "top_p" not in json_data_exclude_none
        assert "stop" not in json_data_exclude_none

    def test_field_validation(self):
        """Test CompletionRequest field validation."""
        # Test temperature bounds
        with pytest.raises(ValidationError):
            CompletionRequest(
                model="gpt-3.5-turbo",
                prompt="Test",
                temperature=2.5  # Should be between 0 and 2
            )

        # Test max_tokens positive
        with pytest.raises(ValidationError):
            CompletionRequest(
                model="gpt-3.5-turbo",
                prompt="Test",
                max_tokens=-1  # Should be positive
            )

    def test_files_field(self):
        """Test CompletionRequest files field."""
        files = ["s3://bucket/doc1.pdf", "s3://bucket/doc2.txt"]
        request = CompletionRequest(
            model="gpt-4",
            prompt="Analyze these files",
            files=files
        )

        assert request.files == files
        assert len(request.files) == 2


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_minimal_creation(self):
        """Test creating ChatCompletionRequest with minimal required fields."""
        messages = [{"role": "user", "content": "Hello!"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == messages
        assert request.max_tokens is None
        assert request.temperature is None

    def test_complex_messages(self):
        """Test ChatCompletionRequest with complex message structures."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What's the weather like?"},
            {"role": "assistant", "content": "I don't have access to current weather data."},
            {"role": "user", "content": "Can you help with something else?"}
        ]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=200,
            temperature=0.8
        )

        assert request.model == "gpt-4"
        assert len(request.messages) == 4
        assert request.messages[0]["role"] == "system"
        assert request.max_tokens == 200
        assert request.temperature == 0.8

    def test_missing_required_fields(self):
        """Test ChatCompletionRequest validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            ChatCompletionRequest(messages=[{"role": "user", "content": "Hi"}])  # Missing model

        assert "model" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            ChatCompletionRequest(model="gpt-3.5-turbo")  # Missing messages

        assert "messages" in str(exc_info.value)

    def test_empty_messages(self):
        """Test ChatCompletionRequest with empty messages list."""
        with pytest.raises(ValidationError) as exc_info:
            ChatCompletionRequest(
                model="gpt-3.5-turbo",
                messages=[]  # Empty messages
            )

        assert "messages" in str(exc_info.value)

    def test_json_serialization(self):
        """Test ChatCompletionRequest JSON serialization."""
        messages = [{"role": "user", "content": "Test message"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.7
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == messages
        assert json_data["temperature"] == 0.7

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "max_tokens" not in json_data_exclude_none
        assert "top_p" not in json_data_exclude_none

    def test_optional_parameters(self):
        """Test ChatCompletionRequest with various optional parameters."""
        messages = [{"role": "user", "content": "Hello"}]
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=150,
            temperature=0.9,
            top_p=0.95,
            n=1,
            stream=False,
            stop=["END"],
            presence_penalty=0.1,
            frequency_penalty=0.2,
            logit_bias={"token_id": 0.5},
            user="test_user"
        )

        assert request.max_tokens == 150
        assert request.temperature == 0.9
        assert request.top_p == 0.95
        assert request.stop == ["END"]
        assert request.user == "test_user"


class TestLLMModelsIntegration:
    """Integration tests for LLM models."""

    def test_model_compatibility(self):
        """Test that both models work with same model names."""
        model_name = "gpt-3.5-turbo"

        completion_request = CompletionRequest(
            model=model_name,
            prompt="Complete this"
        )

        chat_request = ChatCompletionRequest(
            model=model_name,
            messages=[{"role": "user", "content": "Chat with me"}]
        )

        assert completion_request.model == chat_request.model == model_name

    def test_serialization_consistency(self):
        """Test that both models serialize consistently."""
        completion_request = CompletionRequest(
            model="gpt-4",
            prompt="Test prompt",
            temperature=0.7,
            max_tokens=100
        )

        chat_request = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test message"}],
            temperature=0.7,
            max_tokens=100
        )

        completion_json = completion_request.model_dump()
        chat_json = chat_request.model_dump()

        # Both should have consistent field names and types
        assert completion_json["model"] == chat_json["model"]
        assert completion_json["temperature"] == chat_json["temperature"]
        assert completion_json["max_tokens"] == chat_json["max_tokens"]

    def test_realistic_usage_patterns(self):
        """Test realistic usage patterns for both models."""
        # Completion for text generation
        completion = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Write a short story about",
            max_tokens=500,
            temperature=0.8,
            files=["s3://bucket/context.txt"]
        )

        # Chat for conversation
        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative writing assistant."},
                {"role": "user", "content": "Help me write a story."}
            ],
            max_tokens=500,
            temperature=0.8
        )

        assert completion.model == "gpt-3.5-turbo"
        assert chat.model == "gpt-4"
        assert completion.temperature == chat.temperature == 0.8
        assert completion.max_tokens == chat.max_tokens == 500
