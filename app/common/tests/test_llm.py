"""Tests for common.models.llm module."""

import json
from typing import Dict, List, Optional

import pytest
from pydantic import ValidationError

from common.models.llm import ChatCompletionRequest, CompletionRequest, TokenCountRequest, EmbeddingRequest


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_completion_request_basic_creation(self):
        """Test creating CompletionRequest with basic required fields."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Hello, world!"
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.prompt == "Hello, world!"
        assert request.files is None

    def test_completion_request_with_files(self):
        """Test CompletionRequest with files parameter."""
        files = [
            "s3://bucket/file1.txt",
            "arn:aws:s3:::bucket/file2.pdf"
        ]

        request = CompletionRequest(
            model="gpt-4",
            prompt="Analyze these files",
            files=files
        )

        assert request.model == "gpt-4"
        assert request.files == files
        assert len(request.files) == 2

    def test_completion_request_empty_files_list(self):
        """Test CompletionRequest with empty files list."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test prompt",
            files=[]
        )

        assert request.files == []
        assert isinstance(request.files, list)

    def test_completion_request_none_files(self):
        """Test CompletionRequest with None files."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test prompt",
            files=None
        )

        assert request.files is None

    def test_completion_request_inherits_from_litellm(self):
        """Test that CompletionRequest inherits from litellm CompletionRequest."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test"
        )

        # Should have litellm fields
        assert hasattr(request, "model")
        assert hasattr(request, "prompt")

    def test_completion_request_json_serialization(self):
        """Test CompletionRequest JSON serialization."""
        files = ["s3://bucket/file.txt"]
        request = CompletionRequest(
            model="gpt-4",
            prompt="Test prompt",
            files=files,
            max_tokens=100,
            temperature=0.7
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-4"
        assert json_data["prompt"] == "Test prompt"
        assert json_data["files"] == files
        assert json_data["max_tokens"] == 100
        assert json_data["temperature"] == 0.7

    def test_completion_request_additional_litellm_fields(self):
        """Test CompletionRequest with additional litellm fields."""
        # Note: LiteLLM CompletionRequest expects stop to be a dict, not a list
        stop_dict = {"sequences": ["END"]}
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test",
            max_tokens=150,
            temperature=0.8,
            top_p=0.9,
            stop=stop_dict,
            files=["s3://bucket/doc.pdf"]
        )

        assert request.max_tokens == 150
        assert request.temperature == 0.8
        assert request.top_p == 0.9
        assert request.stop == stop_dict
        assert request.files == ["s3://bucket/doc.pdf"]

    def test_completion_request_files_field_description(self):
        """Test that files field has the correct description."""
        # Create request to access field info
        request = CompletionRequest(model="test", prompt="test")

        # Check if the field exists and is properly defined
        assert hasattr(request, "files")

    def test_completion_request_complex_files_list(self):
        """Test CompletionRequest with complex files list."""
        files = [
            "s3://production-bucket/documents/report.pdf",
            "arn:aws:s3:::analytics-bucket/data/dataset.csv",
            "https://presigned-url.amazonaws.com/temp/upload.docx"
        ]

        request = CompletionRequest(
            model="gpt-4",
            prompt="Analyze these documents",
            files=files
        )

        assert request.files == files
        assert all(isinstance(file, str) for file in request.files)


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_chat_completion_request_minimal_creation(self):
        """Test creating ChatCompletionRequest with minimal required fields."""
        messages = [
            {"role": "user", "content": "Hello"}
        ]

        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == messages
        assert request.files is None
        assert request.temperature is None
        assert request.max_tokens is None

    def test_chat_completion_request_all_fields(self):
        """Test ChatCompletionRequest with all fields populated."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi there!"},
            {"role": "user", "content": "How are you?"}
        ]

        files = ["s3://bucket/context.txt"]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            temperature=0.7,
            top_p=0.9,
            n=1,
            stream=False,
            stop=["END"],
            max_tokens=500,
            presence_penalty=0.1,
            frequency_penalty=0.2,
            logit_bias={"token_id": 0.5},
            user="user_123",
            response_format={"type": "json"},
            seed=42,
            tools=["calculator"],
            tool_choice="auto",
            files=files
        )

        assert request.model == "gpt-4"
        assert request.messages == messages
        assert request.temperature == 0.7
        assert request.top_p == 0.9
        assert request.n == 1
        assert request.stream == False
        assert request.stop == ["END"]
        assert request.max_tokens == 500
        assert request.presence_penalty == 0.1
        assert request.frequency_penalty == 0.2
        assert request.logit_bias == {"token_id": 0.5}
        assert request.user == "user_123"
        assert request.response_format == {"type": "json"}
        assert request.seed == 42
        assert request.tools == ["calculator"]
        assert request.tool_choice == "auto"
        assert request.files == files

    def test_chat_completion_request_missing_required_fields(self):
        """Test ChatCompletionRequest creation with missing required fields."""
        with pytest.raises(ValidationError):
            ChatCompletionRequest(model="gpt-3.5-turbo")  # Missing messages

        with pytest.raises(ValidationError):
            ChatCompletionRequest(messages=[{"role": "user", "content": "test"}])  # Missing model

    def test_chat_completion_request_empty_messages(self):
        """Test ChatCompletionRequest with empty messages list."""
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[]
        )

        assert request.messages == []
        assert isinstance(request.messages, list)

    def test_chat_completion_request_complex_messages(self):
        """Test ChatCompletionRequest with complex message structures."""
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant with access to tools."
            },
            {
                "role": "user",
                "content": "Calculate 15 * 23 and explain the steps"
            },
            {
                "role": "assistant",
                "content": "I'll calculate that for you",
                "function_call": {
                    "name": "calculator",
                    "arguments": '{"operation": "multiply", "a": 15, "b": 23}'
                }
            },
            {
                "role": "function",
                "name": "calculator",
                "content": "345"
            }
        ]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages
        )

        assert request.messages == messages
        assert len(request.messages) == 4
        assert request.messages[2]["function_call"]["name"] == "calculator"

    def test_chat_completion_request_files_integration(self):
        """Test ChatCompletionRequest files integration with chat messages."""
        messages = [
            {"role": "user", "content": "Analyze the attached documents"}
        ]
        files = [
            "s3://docs-bucket/report.pdf",
            "s3://docs-bucket/data.csv"
        ]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            files=files
        )

        assert request.messages == messages
        assert request.files == files
        assert len(request.files) == 2

    def test_chat_completion_request_json_serialization(self):
        """Test ChatCompletionRequest JSON serialization."""
        messages = [{"role": "user", "content": "Hello"}]

        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.5,
            files=["s3://bucket/file.txt"]
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == messages
        assert json_data["temperature"] == 0.5
        assert json_data["files"] == ["s3://bucket/file.txt"]

    def test_chat_completion_request_json_string_serialization(self):
        """Test ChatCompletionRequest JSON string serialization."""
        messages = [{"role": "user", "content": "Test"}]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages
        )

        json_str = request.model_dump_json()
        parsed = json.loads(json_str)

        assert parsed["model"] == "gpt-4"
        assert parsed["messages"] == messages
        assert parsed.get("files") is None

    def test_chat_completion_request_model_config(self):
        """Test ChatCompletionRequest model configuration."""
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "test"}]
        )

        # Should have model config that allows extra fields
        assert hasattr(request, "model_config") or hasattr(ChatCompletionRequest, "model_config")

    def test_chat_completion_request_extra_fields_allowed(self):
        """Test that ChatCompletionRequest allows extra fields."""
        messages = [{"role": "user", "content": "test"}]

        # This should work due to extra="allow" in model config
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            custom_field="custom_value"  # type: ignore
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == messages

    def test_chat_completion_request_protected_namespaces(self):
        """Test ChatCompletionRequest with protected namespaces configuration."""
        # This test ensures the model_config with protected_namespaces works
        messages = [{"role": "user", "content": "test"}]

        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages
        )

        # Should be created successfully without namespace conflicts
        assert request.model == "gpt-3.5-turbo"

    def test_chat_completion_request_optional_fields_none(self):
        """Test ChatCompletionRequest with optional fields explicitly set to None."""
        messages = [{"role": "user", "content": "test"}]

        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=None,
            top_p=None,
            n=None,
            stream=None,
            stop=None,
            max_tokens=None,
            presence_penalty=None,
            frequency_penalty=None,
            logit_bias=None,
            user=None,
            response_format=None,
            seed=None,
            tools=None,
            tool_choice=None,
            files=None
        )

        assert request.temperature is None
        assert request.top_p is None
        assert request.n is None
        assert request.stream is None
        assert request.stop is None
        assert request.max_tokens is None
        assert request.presence_penalty is None
        assert request.frequency_penalty is None
        assert request.logit_bias is None
        assert request.user is None
        assert request.response_format is None
        assert request.seed is None
        assert request.tools is None
        assert request.tool_choice is None
        assert request.files is None

    def test_chat_completion_request_numeric_validations(self):
        """Test ChatCompletionRequest with boundary numeric values."""
        messages = [{"role": "user", "content": "test"}]

        # Test with boundary values
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.0,
            top_p=1.0,
            n=1,
            max_tokens=1,
            presence_penalty=-2.0,
            frequency_penalty=2.0
        )

        assert request.temperature == 0.0
        assert request.top_p == 1.0
        assert request.n == 1
        assert request.max_tokens == 1
        assert request.presence_penalty == -2.0
        assert request.frequency_penalty == 2.0


class TestTypeAliases:
    """Test cases for type aliases."""

    def test_token_count_request_alias(self):
        """Test TokenCountRequest type alias."""
        # Verify the alias exists and can be imported
        assert TokenCountRequest is not None

        # Should be able to create instances (this tests the alias points to real type)
        try:
            # This may or may not work depending on litellm structure, but alias should exist
            isinstance(TokenCountRequest, type)
        except:
            # If instantiation fails, at least verify the alias exists
            pass

    def test_embedding_request_alias(self):
        """Test EmbeddingRequest type alias."""
        # Verify the alias exists and can be imported
        assert EmbeddingRequest is not None

        # Should be able to create instances (this tests the alias points to real type)
        try:
            # This may or may not work depending on litellm structure, but alias should exist
            isinstance(EmbeddingRequest, type)
        except:
            # If instantiation fails, at least verify the alias exists
            pass

    def test_type_aliases_are_different(self):
        """Test that type aliases point to different types."""
        # The aliases should exist and be distinct
        assert TokenCountRequest is not None
        assert EmbeddingRequest is not None

        # They should be different types
        assert TokenCountRequest != EmbeddingRequest


class TestLLMModelsIntegration:
    """Integration tests for LLM models."""

    def test_models_serialization_consistency(self):
        """Test that all LLM models serialize consistently."""
        # CompletionRequest
        completion = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test",
            files=["s3://bucket/file.txt"]
        )
        completion_json = completion.model_dump()
        assert "files" in completion_json
        assert completion_json["files"] == ["s3://bucket/file.txt"]

        # ChatCompletionRequest
        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test"}],
            files=["s3://bucket/file.txt"]
        )
        chat_json = chat.model_dump()
        assert "files" in chat_json
        assert chat_json["files"] == ["s3://bucket/file.txt"]

    def test_files_field_consistency(self):
        """Test that files field works consistently across models."""
        files = [
            "s3://bucket1/doc1.pdf",
            "arn:aws:s3:::bucket2/doc2.csv"
        ]

        # Both models should handle files the same way
        completion = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Analyze files",
            files=files
        )

        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Analyze files"}],
            files=files
        )

        assert completion.files == files
        assert chat.files == files
        assert completion.files == chat.files

    def test_models_with_realistic_scenarios(self):
        """Test models with realistic usage scenarios."""
        # Document analysis scenario
        files = [
            "s3://legal-docs/contract.pdf",
            "s3://financial-data/quarterly-report.xlsx"
        ]

        # Completion request for document summarization
        completion = CompletionRequest(
            model="gpt-4",
            prompt="Summarize the key points from these documents",
            files=files,
            max_tokens=1000,
            temperature=0.3
        )

        # Chat request for interactive document analysis
        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a document analysis assistant"},
                {"role": "user", "content": "Analyze these documents and answer questions"}
            ],
            files=files,
            temperature=0.5,
            max_tokens=1500,
            tools=["document_search", "calculation"]
        )

        # Verify both work with realistic data
        assert completion.model == "gpt-4"
        assert completion.files == files
        assert chat.model == "gpt-4"
        assert chat.files == files
        assert len(chat.messages) == 2
        assert chat.tools == ["document_search", "calculation"]

    def test_empty_and_none_files_handling(self):
        """Test how models handle empty and None files."""
        # Test None files
        completion_none = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test",
            files=None
        )

        chat_none = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Test"}],
            files=None
        )

        # Test empty files
        completion_empty = CompletionRequest(
            model="gpt-3.5-turbo",
            prompt="Test",
            files=[]
        )

        chat_empty = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Test"}],
            files=[]
        )

        assert completion_none.files is None
        assert chat_none.files is None
        assert completion_empty.files == []
        assert chat_empty.files == []
