"""Concise tests for common.models.llm module."""

import pytest
from pydantic import ValidationError

from common.models.llm import CompletionRequest, ChatCompletionRequest


class TestCompletionRequest:
    """Test cases for CompletionRequest model."""

    def test_minimal_creation(self):
        """Test creating CompletionRequest with minimal required fields."""
        request = CompletionRequest(
            model="gpt-3.5-turbo"
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.files is None
        assert request.max_tokens is None
        assert request.temperature is None

    def test_all_fields(self):
        """Test CompletionRequest with all fields populated."""
        request = CompletionRequest(
            model="gpt-4",
            messages=["Complete this text"],
            max_tokens=150,
            temperature=0.7,
            top_p=0.9,
            n=1,
            stream=False,
            logprobs=None,
            stop=None,
            presence_penalty=0.0,
            frequency_penalty=0.0,
            logit_bias=None,
            user="test_user",
            files=["s3://bucket/doc.pdf"]
        )

        assert request.model == "gpt-4"
        assert request.messages == ["Complete this text"]
        assert request.max_tokens == 150
        assert request.temperature == 0.7
        assert request.top_p == 0.9
        assert request.files == ["s3://bucket/doc.pdf"]
        assert request.user == "test_user"

    def test_missing_required_fields(self):
        """Test CompletionRequest validation with missing required fields."""
        # LiteLLM CompletionRequest only requires 'model', not 'prompt'
        with pytest.raises(ValidationError) as exc_info:
            CompletionRequest()  # Missing model

        assert "model" in str(exc_info.value)

        # Test that model alone is sufficient (no prompt required)
        request = CompletionRequest(model="gpt-3.5-turbo")
        assert request.model == "gpt-3.5-turbo"

    def test_json_serialization(self):
        """Test CompletionRequest JSON serialization."""
        request = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=["Test message"],
            max_tokens=100,
            temperature=0.5
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == ["Test message"]
        assert json_data["max_tokens"] == 100
        assert json_data["temperature"] == 0.5

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "top_p" not in json_data_exclude_none
        assert "stop" not in json_data_exclude_none

    def test_field_validation(self):
        """Test CompletionRequest field validation."""
        # LiteLLM CompletionRequest is more permissive than expected
        # Test that extreme values are accepted (LiteLLM handles validation internally)
        request1 = CompletionRequest(
            model="gpt-3.5-turbo",
            temperature=2.5  # LiteLLM allows this
        )
        assert request1.temperature == 2.5

        request2 = CompletionRequest(
            model="gpt-3.5-turbo",
            max_tokens=-1  # LiteLLM allows this
        )
        assert request2.max_tokens == -1

    def test_files_field(self):
        """Test CompletionRequest files field."""
        files = ["s3://bucket/doc1.pdf", "s3://bucket/doc2.txt"]
        request = CompletionRequest(
            model="gpt-4",
            messages=["Analyze these files"],
            files=files
        )

        assert request.files == files
        assert len(request.files) == 2


class TestChatCompletionRequest:
    """Test cases for ChatCompletionRequest model."""

    def test_minimal_creation(self):
        """Test creating ChatCompletionRequest with minimal required fields."""
        messages = [{"role": "user", "content": "Hello!"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == messages
        assert request.max_tokens is None
        assert request.temperature is None

    def test_complex_messages(self):
        """Test ChatCompletionRequest with complex message structures."""
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "What's the weather like?"},
            {"role": "assistant", "content": "I don't have access to current weather data."},
            {"role": "user", "content": "Can you help with something else?"}
        ]

        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=200,
            temperature=0.8
        )

        assert request.model == "gpt-4"
        assert len(request.messages) == 4
        assert request.messages[0]["role"] == "system"
        assert request.max_tokens == 200
        assert request.temperature == 0.8

    def test_missing_required_fields(self):
        """Test ChatCompletionRequest validation with missing required fields."""
        with pytest.raises(ValidationError) as exc_info:
            ChatCompletionRequest(messages=[{"role": "user", "content": "Hi"}])  # Missing model

        assert "model" in str(exc_info.value)

        with pytest.raises(ValidationError) as exc_info:
            ChatCompletionRequest(model="gpt-3.5-turbo")  # Missing messages

        assert "messages" in str(exc_info.value)

    def test_empty_messages(self):
        """Test ChatCompletionRequest with empty messages list."""
        # Our ChatCompletionRequest allows empty messages (no validation constraint)
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=[]  # Empty messages allowed
        )

        assert request.model == "gpt-3.5-turbo"
        assert request.messages == []

    def test_json_serialization(self):
        """Test ChatCompletionRequest JSON serialization."""
        messages = [{"role": "user", "content": "Test message"}]
        request = ChatCompletionRequest(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=0.7
        )

        json_data = request.model_dump()
        assert json_data["model"] == "gpt-3.5-turbo"
        assert json_data["messages"] == messages
        assert json_data["temperature"] == 0.7

        # Test excluding None values
        json_data_exclude_none = request.model_dump(exclude_none=True)
        assert "max_tokens" not in json_data_exclude_none
        assert "top_p" not in json_data_exclude_none

    def test_optional_parameters(self):
        """Test ChatCompletionRequest with various optional parameters."""
        messages = [{"role": "user", "content": "Hello"}]
        request = ChatCompletionRequest(
            model="gpt-4",
            messages=messages,
            max_tokens=150,
            temperature=0.9,
            top_p=0.95,
            n=1,
            stream=False,
            stop=["END"],
            presence_penalty=0.1,
            frequency_penalty=0.2,
            logit_bias={"token_id": 0.5},
            user="test_user"
        )

        assert request.max_tokens == 150
        assert request.temperature == 0.9
        assert request.top_p == 0.95
        assert request.stop == ["END"]
        assert request.user == "test_user"


class TestLLMModelsIntegration:
    """Integration tests for LLM models."""

    def test_model_compatibility(self):
        """Test that both models work with same model names."""
        model_name = "gpt-3.5-turbo"

        completion_request = CompletionRequest(
            model=model_name,
            messages=["Complete this"]
        )

        chat_request = ChatCompletionRequest(
            model=model_name,
            messages=[{"role": "user", "content": "Chat with me"}]
        )

        assert completion_request.model == chat_request.model == model_name

    def test_serialization_consistency(self):
        """Test that both models serialize consistently."""
        completion_request = CompletionRequest(
            model="gpt-4",
            messages=["Test message"],
            temperature=0.7,
            max_tokens=100
        )

        chat_request = ChatCompletionRequest(
            model="gpt-4",
            messages=[{"role": "user", "content": "Test message"}],
            temperature=0.7,
            max_tokens=100
        )

        completion_json = completion_request.model_dump()
        chat_json = chat_request.model_dump()

        # Both should have consistent field names and types
        assert completion_json["model"] == chat_json["model"]
        assert completion_json["temperature"] == chat_json["temperature"]
        assert completion_json["max_tokens"] == chat_json["max_tokens"]

    def test_realistic_usage_patterns(self):
        """Test realistic usage patterns for both models."""
        # Completion for text generation
        completion = CompletionRequest(
            model="gpt-3.5-turbo",
            messages=["Write a short story about"],
            max_tokens=500,
            temperature=0.8,
            files=["s3://bucket/context.txt"]
        )

        # Chat for conversation
        chat = ChatCompletionRequest(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a creative writing assistant."},
                {"role": "user", "content": "Help me write a story."}
            ],
            max_tokens=500,
            temperature=0.8
        )

        assert completion.model == "gpt-3.5-turbo"
        assert chat.model == "gpt-4"
        assert completion.temperature == chat.temperature == 0.8
        assert completion.max_tokens == chat.max_tokens == 500
