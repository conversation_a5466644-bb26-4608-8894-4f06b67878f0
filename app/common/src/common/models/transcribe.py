"""Pydantic models for the transcription workflow."""

from typing import Annotated, Optional, Union

from fastapi import File, Form, UploadFile
from pydantic import BaseModel, ConfigDict, Field


class TranscriptionRequest(BaseModel):
    """Request model for audio or video transcription."""

    file: Union[UploadFile, str] = Field(..., description="Media file stream or presigned S3 URL")
    language_code: Optional[str] = Field(default=None, description="Language code of the media content")
    media_format: Optional[str] = Field(default=None, description="Format of the media file, e.g. mp3")
    user: Optional[str] = Field(default=None, description="User identifier for the request")

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @classmethod
    def as_form(
        cls,
        file: Optional[Annotated[UploadFile, File(description="Media file")]] = None,
        file_url: Optional[Annotated[str, Form()]] = None,
        language_code: Optional[Annotated[str, Form()]] = None,
        user: Optional[Annotated[str, Form()]] = None,
    ) -> "TranscriptionRequest":
        """Create a model instance from form data."""
        file_value: Union[UploadFile, str]
        if file is not None:
            file_value = file
        elif file_url is not None:
            file_value = file_url
        else:
            raise ValueError("file or file_url must be provided")
        return cls(file=file_value, language_code=language_code, user=user)


class TranscriptionJobResponse(BaseModel):
    """Response model for submitted transcription jobs."""

    job_id: str = Field(..., description="UUID of the submitted job")

    class Config:  # pylint: disable=too-few-public-methods
        """Configuration class for TranscriptionJobResponse model."""

        frozen = True
