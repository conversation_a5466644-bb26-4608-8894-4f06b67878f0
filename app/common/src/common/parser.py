"""Common utility functions for parsing data."""

import json
import re

from common.logging import LoggingSetup

log = LoggingSetup.setup_logger("parser")


def extract_and_parse_json(content: str):
    """
    Extract and parse JSON content from a string.

    Args:
        content (str): The string to parse

    Returns:
        dict: The parsed JSON content
    """
    try:
        # First, look for markdown code blocks (case-insensitive)
        # Match ```json, ```JSON, ``` json, etc. and capture the content
        markdown_pattern = r"```\s*(?:json\s*)?\s*([\s\S]*?)\s*```"
        markdown_match = re.search(markdown_pattern, content, re.IGNORECASE)
        if markdown_match:
            json_content = markdown_match.group(1).strip()
            return json.loads(json_content, strict=False)

        # handle if header text is present
        # e.g. "here is the json you are looking for\n{...}"
        # Try to find valid JSON objects/arrays, preferring complete ones
        json_patterns = [
            r'(\{[^{}]*\})',  # Simple objects without nested braces
            r'(\[[^\[\]]*\])',  # Simple arrays without nested brackets
            r'(\{.*\})',  # Any object (greedy)
            r'(\[.*\])',  # Any array (greedy)
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match.strip(), strict=False)
                except json.JSONDecodeError:
                    continue

        # Finally, try parsing the entire content
        return json.loads(content.strip(), strict=False)

    except json.decoder.JSONDecodeError as e:
        log.error("JSON parsing error: %s", e)
        raise e
    except Exception as e:
        log.error("Unexpected error while parsing output: %s", e)
        raise e
